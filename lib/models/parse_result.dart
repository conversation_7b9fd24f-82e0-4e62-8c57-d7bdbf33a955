import 'transaction_model.dart';

/// Data transfer object for communicating parsing results between the ML Kit parser and the UI
class ParseResult {
  final Transaction transaction;
  final bool needsCategorySelection;
  final String? error;

  const ParseResult({
    required this.transaction,
    required this.needsCategorySelection,
    this.error,
  });

  /// Factory constructor for successful parsing without category selection needed
  factory ParseResult.success(Transaction transaction) {
    return ParseResult(
      transaction: transaction,
      needsCategorySelection: false,
    );
  }

  /// Factory constructor for successful parsing but category selection needed
  factory ParseResult.needsCategory(Transaction transaction) {
    return ParseResult(
      transaction: transaction,
      needsCategorySelection: true,
    );
  }

  /// Factory constructor for failed parsing
  factory ParseResult.failed(Transaction fallbackTransaction, String error) {
    return ParseResult(
      transaction: fallbackTransaction,
      needsCategorySelection: true,
      error: error,
    );
  }

  /// Helper method to check if parsing was successful
  bool get isSuccess => error == null;

  /// Helper method to check if user input is required
  bool get requiresUserInput => needsCategorySelection;

  /// Helper method to check if there was an error
  bool get hasError => error != null;

  @override
  String toString() {
    return 'ParseResult{transaction: $transaction, needsCategorySelection: $needsCategorySelection, error: $error}';
  }
}
