import 'package:intl/intl.dart';

class CurrencyUtils {
  // Static map of currency codes to their symbols
  static const Map<String, String> currencySymbols = {
    'USD': '\$',
    'EUR': '€',
    'GBP': '£',
    'JPY': '¥',
    'CNY': '¥',
    'INR': '₹',
    'KRW': '₩',
    'AUD': 'A\$',
    'CAD': 'C\$',
    'CHF': 'CHF',
    'SEK': 'kr',
    'NOK': 'kr',
    'DKK': 'kr',
    'RUB': '₽',
    'BRL': 'R\$',
    'MXN': '\$',
    'ZAR': 'R',
    'SGD': 'S\$',
    'HKD': 'HK\$',
    'NZD': 'NZ\$',
    'THB': '฿',
    'TRY': '₺',
    'PLN': 'zł',
    'CZK': 'Kč',
    'HUF': 'Ft',
    'ILS': '₪',
    'AED': 'د.إ',
    'SAR': '﷼',
    'EGP': '£',
    'QAR': '﷼',
    'KWD': 'د.ك',
    'BHD': '.د.ب',
    'OMR': '﷼',
    'JOD': 'د.ا',
    'LBP': '£',
    'MAD': 'د.م.',
    'TND': 'د.ت',
    'DZD': 'د.ج',
    'LYD': 'ل.د',
    'SDG': 'ج.س.',
    'ETB': 'Br',
    'KES': 'KSh',
    'UGX': 'USh',
    'TZS': 'TSh',
    'GHS': '¢',
    'NGN': '₦',
    'XOF': 'CFA',
    'XAF': 'FCFA',
    'MWK': 'MK',
    'ZMW': 'ZK',
    'BWP': 'P',
    'NAD': '\$',
    'SZL': 'L',
    'LSL': 'L',
    'MZN': 'MT',
    'AOA': 'Kz',
    'CVE': '\$',
    'GMD': 'D',
    'GNF': 'FG',
    'LRD': '\$',
    'SLL': 'Le',
    'STN': 'Db',
    'CDF': 'FC',
    'RWF': 'R₣',
    'BIF': 'FBu',
    'DJF': 'Fdj',
    'ERN': 'Nfk',
    'SOS': 'S',
    'SCR': '₨',
    'MUR': '₨',
    'MGA': 'Ar',
    'KMF': 'CF',
    'MYR': 'RM',
    'IDR': 'Rp',
    'PHP': '₱',
    'VND': '₫',
    'LAK': '₭',
    'KHR': '៛',
    'MMK': 'K',
    'BDT': '৳',
    'LKR': '₨',
    'MVR': '.ރ',
    'NPR': '₨',
    'BTN': 'Nu.',
    'PKR': '₨',
    'AFN': '؋',
    'IRR': '﷼',
    'IQD': 'ع.د',
    'SYP': '£',
    'YER': '﷼',
    'UZS': 'лв',
    'KZT': '₸',
    'KGS': 'лв',
    'TJS': 'SM',
    'TMT': 'T',
    'AZN': '₼',
    'GEL': '₾',
    'AMD': '֏',
    'BGN': 'лв',
    'RON': 'lei',
    'HRK': 'kn',
    'RSD': 'дин.',
    'BAM': 'KM',
    'MKD': 'ден',
    'ALL': 'L',
    'MDL': 'L',
    'UAH': '₴',
    'BYN': 'Br',
    'LTL': 'Lt',
    'LVL': 'Ls',
    'EEK': 'kr',
    'ISK': 'kr',
    'FJD': '\$',
    'PGK': 'K',
    'SBD': '\$',
    'VUV': 'VT',
    'WST': 'T',
    'TOP': 'T\$',
    'TWD': 'NT\$',
    'MNT': '₮',
    'KPW': '₩',
    'CLP': '\$',
    'ARS': '\$',
    'UYU': '\$U',
    'PYG': 'Gs',
    'BOB': 'Bs',
    'PEN': 'S/',
    'COP': '\$',
    'VES': 'Bs',
    'GYD': '\$',
    'SRD': '\$',
    'FKP': '£',
    'SHP': '£',
    'GIP': '£',
    'JEP': '£',
    'GGP': '£',
    'IMP': '£',
    'XCD': '\$',
    'BBD': '\$',
    'BZD': 'BZ\$',
    'BMD': '\$',
    'KYD': '\$',
    'JMD': 'J\$',
    'TTD': 'TT\$',
    'HTG': 'G',
    'DOP': 'RD\$',
    'CUP': '₱',
    'NIO': 'C\$',
    'CRC': '₡',
    'GTQ': 'Q',
    'HNL': 'L',
    'PAB': 'B/.',
    'SVC': '₡',
  };

  // List of supported currencies with their details
  static const List<Map<String, String>> supportedCurrencies = [
    {'code': 'USD', 'symbol': '\$', 'name': 'US Dollar'},
    {'code': 'EUR', 'symbol': '€', 'name': 'Euro'},
    {'code': 'GBP', 'symbol': '£', 'name': 'British Pound'},
    {'code': 'JPY', 'symbol': '¥', 'name': 'Japanese Yen'},
    {'code': 'CNY', 'symbol': '¥', 'name': 'Chinese Yuan'},
    {'code': 'INR', 'symbol': '₹', 'name': 'Indian Rupee'},
    {'code': 'KRW', 'symbol': '₩', 'name': 'South Korean Won'},
    {'code': 'AUD', 'symbol': 'A\$', 'name': 'Australian Dollar'},
    {'code': 'CAD', 'symbol': 'C\$', 'name': 'Canadian Dollar'},
    {'code': 'CHF', 'symbol': 'CHF', 'name': 'Swiss Franc'},
    {'code': 'SEK', 'symbol': 'kr', 'name': 'Swedish Krona'},
    {'code': 'NOK', 'symbol': 'kr', 'name': 'Norwegian Krone'},
    {'code': 'DKK', 'symbol': 'kr', 'name': 'Danish Krone'},
    {'code': 'RUB', 'symbol': '₽', 'name': 'Russian Ruble'},
    {'code': 'BRL', 'symbol': 'R\$', 'name': 'Brazilian Real'},
    {'code': 'MXN', 'symbol': '\$', 'name': 'Mexican Peso'},
    {'code': 'ZAR', 'symbol': 'R', 'name': 'South African Rand'},
    {'code': 'SGD', 'symbol': 'S\$', 'name': 'Singapore Dollar'},
    {'code': 'HKD', 'symbol': 'HK\$', 'name': 'Hong Kong Dollar'},
    {'code': 'NZD', 'symbol': 'NZ\$', 'name': 'New Zealand Dollar'},
    {'code': 'THB', 'symbol': '฿', 'name': 'Thai Baht'},
    {'code': 'TRY', 'symbol': '₺', 'name': 'Turkish Lira'},
    {'code': 'PLN', 'symbol': 'zł', 'name': 'Polish Zloty'},
    {'code': 'CZK', 'symbol': 'Kč', 'name': 'Czech Koruna'},
    {'code': 'HUF', 'symbol': 'Ft', 'name': 'Hungarian Forint'},
    {'code': 'ILS', 'symbol': '₪', 'name': 'Israeli Shekel'},
    {'code': 'AED', 'symbol': 'د.إ', 'name': 'UAE Dirham'},
    {'code': 'SAR', 'symbol': '﷼', 'name': 'Saudi Riyal'},
    {'code': 'MYR', 'symbol': 'RM', 'name': 'Malaysian Ringgit'},
    {'code': 'IDR', 'symbol': 'Rp', 'name': 'Indonesian Rupiah'},
    {'code': 'PHP', 'symbol': '₱', 'name': 'Philippine Peso'},
    {'code': 'VND', 'symbol': '₫', 'name': 'Vietnamese Dong'},
    {'code': 'TWD', 'symbol': 'NT\$', 'name': 'Taiwan Dollar'},
  ];

  /// Get the currency symbol for a given currency code
  /// Returns the symbol if found, otherwise returns the currency code itself
  static String getCurrencySymbol(String currencyCode) {
    return currencySymbols[currencyCode.toUpperCase()] ?? currencyCode;
  }

  /// Format a currency amount with the appropriate symbol and formatting
  /// Uses NumberFormat.currency() for proper locale-specific formatting
  static String formatCurrencyAmount(double amount, String currencyCode) {
    try {
      final symbol = getCurrencySymbol(currencyCode);
      return NumberFormat.currency(
        symbol: symbol,
        decimalDigits: _getDecimalDigits(currencyCode),
      ).format(amount);
    } catch (e) {
      // Fallback to simple formatting if NumberFormat fails
      final symbol = getCurrencySymbol(currencyCode);
      return '$symbol${amount.toStringAsFixed(_getDecimalDigits(currencyCode))}';
    }
  }

  /// Get the number of decimal digits for a currency
  /// Most currencies use 2 decimal places, but some (like JPY, KRW) use 0
  static int _getDecimalDigits(String currencyCode) {
    final noDecimalCurrencies = ['JPY', 'KRW', 'VND', 'CLP', 'PYG', 'RWF', 'UGX', 'KMF', 'GNF', 'MGA', 'XAF', 'XOF'];
    return noDecimalCurrencies.contains(currencyCode.toUpperCase()) ? 0 : 2;
  }

  /// Map currency symbols to currency codes
  /// Used for parsing text that contains currency symbols
  static String symbolToCurrencyCode(String symbol) {
    for (final entry in currencySymbols.entries) {
      if (entry.value == symbol) {
        return entry.key;
      }
    }
    return 'USD'; // Default fallback
  }

  /// Check if a currency code is supported
  static bool isSupportedCurrency(String currencyCode) {
    return currencySymbols.containsKey(currencyCode.toUpperCase());
  }

  /// Get all supported currency codes
  static List<String> getSupportedCurrencyCodes() {
    return supportedCurrencies.map((currency) => currency['code']!).toList();
  }

  /// Get currency name by code
  static String getCurrencyName(String currencyCode) {
    final currency = supportedCurrencies.firstWhere(
      (currency) => currency['code'] == currencyCode.toUpperCase(),
      orElse: () => {'name': currencyCode},
    );
    return currency['name']!;
  }
}
