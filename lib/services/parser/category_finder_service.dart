import '../../models/transaction_model.dart';
import '../storage_service.dart';
import 'learned_category_storage.dart';
import 'category_keyword_map.dart';

/// Service for finding transaction categories using keyword matching and learned associations
class CategoryFinderService {
  final LearnedCategoryStorage _learnedStorage;

  CategoryFinderService(StorageService storageService)
      : _learnedStorage = LearnedCategoryStorage(storageService);

  /// Find category for the given text and transaction type
  /// Returns null if no category can be determined (triggering user selection)
  Future<String?> findCategory(String remainingText, TransactionType type) async {
    if (remainingText.trim().isEmpty) return null;

    // First check learned history
    final learnedCategory = await _learnedStorage.getLearnedCategory(remainingText);
    if (learnedCategory != null) {
      return learnedCategory;
    }

    // Fall back to keyword matching
    final keywordCategory = findCategoryByKeywords(remainingText);
    if (keywordCategory != null) {
      return keywordCategory;
    }

    // No category found
    return null;
  }

  /// Save a user's category selection for future learning
  Future<void> learnCategory(String text, String categoryId) async {
    await _learnedStorage.saveLearnedCategory(text, categoryId);
  }

  /// Get all learned associations for debugging
  Future<Map<String, String>> getAllLearnedCategories() async {
    return await _learnedStorage.getAllLearnedCategories();
  }

  /// Clear all learned data
  Future<void> clearLearnedData() async {
    await _learnedStorage.clearLearnedData();
  }

  /// Export learned data for debugging
  Future<String?> exportLearnedData() async {
    return await _learnedStorage.exportLearnedData();
  }

  /// Get available category IDs for keyword matching
  List<String> getAvailableCategoryIds() {
    return getAllCategoryIds();
  }

  /// Get keywords for a specific category
  List<String> getKeywordsForCategory(String categoryId) {
    return categoryKeywords[categoryId] ?? [];
  }
}
