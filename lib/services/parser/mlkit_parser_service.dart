import 'package:google_mlkit_entity_extraction/google_mlkit_entity_extraction.dart';
import 'package:uuid/uuid.dart';
import '../../models/transaction_model.dart';
import '../../models/parse_result.dart';
import '../../utils/currency_utils.dart';
import '../storage_service.dart';
import 'category_finder_service.dart';
import 'fallback_parser_service.dart';

/// Main orchestrator service that coordinates the entire parsing pipeline using ML Kit with fallback
class MlKitParserService {
  static MlKitParserService? _instance;
  
  EntityExtractor? _entityExtractor;
  late CategoryFinderService _categoryFinder;
  late FallbackParserService _fallbackParser;
  late StorageService _storageService;
  final Uuid _uuid = const Uuid();
  bool _isInitialized = false;
  bool _mlKitAvailable = false;

  MlKitParserService._();

  /// Factory constructor to get singleton instance
  static Future<MlKitParserService> getInstance(StorageService storageService) async {
    if (_instance == null) {
      _instance = MlKitParserService._();
      _instance!._storageService = storageService;
      _instance!._categoryFinder = CategoryFinderService(storageService);
      _instance!._fallbackParser = FallbackParserService(storageService);
      await _instance!._initialize();
    }
    return _instance!;
  }

  /// Initialize ML Kit models with fallback support
  Future<void> _initialize() async {
    if (_isInitialized) return;
    
    try {
      print('Attempting to initialize ML Kit...');
      // Check if model is available, download if needed
      final manager = EntityExtractorModelManager();
      const languageTag = 'en'; // Use language tag instead of enum
      final isDownloaded = await manager.isModelDownloaded(languageTag);
      
      if (!isDownloaded) {
        print('Downloading ML Kit model...');
        await manager.downloadModel(languageTag);
        print('ML Kit model downloaded successfully');
      }
      
      _entityExtractor = EntityExtractor(language: EntityExtractorLanguage.english);
      _mlKitAvailable = true;
      print('ML Kit initialized successfully');
      
    } catch (e) {
      print('Error initializing ML Kit: $e');
      print('Falling back to regex-based parsing');
      _mlKitAvailable = false;
      // ML Kit will be null, will use fallback parser
    }
    
    _isInitialized = true;
  }

  /// Main entry point for parsing transactions
  Future<ParseResult> parseTransaction(String text) async {
    try {
      if (!_isInitialized) {
        await _initialize();
      }

      // If ML Kit is available, try ML Kit first
      if (_mlKitAvailable && _entityExtractor != null) {
        try {
          return await _parseWithMLKit(text);
        } catch (e) {
          print('ML Kit parsing failed, falling back to regex: $e');
          // Fall through to regex fallback
        }
      }

      // Use fallback parser (regex-based)
      return await _fallbackParser.parseTransaction(text);
      
    } catch (e) {
      return ParseResult.failed(
        _createFallbackTransaction(text),
        'Parsing failed: $e'
      );
    }
  }

  /// Parse using ML Kit
  Future<ParseResult> _parseWithMLKit(String text) async {
    print('Parsing transaction with ML Kit...');

    // Extract entities using ML Kit
    final entities = await _entityExtractor!.annotateText(text);

    // Enhanced fallback logic: check if MLKit returns incomplete results
    if (entities.isEmpty) {
      print('MLKit returned no entities, falling back to regex parser');
      return await _fallbackParser.parseTransaction(text);
    }

    // Extract money and datetime entities
    double? amount;
    String? currencyCode;
    DateTime? extractedDate;
    String remainingText = text;
    bool foundMoneyEntity = false;

    for (final entity in entities) {
      // Check entity type using runtime type checking
      if (entity.runtimeType.toString().contains('Money')) {
        foundMoneyEntity = true;
        final result = _parseMoneyEntity(entity);
        if (result != null) {
          amount = result['amount'] as double?;
          currencyCode = result['currency'] as String?;
        }
        remainingText = _removeEntityFromText(remainingText, entity);
      } else if (entity.runtimeType.toString().contains('DateTime')) {
        extractedDate = _parseDateTimeEntity(entity);
        remainingText = _removeEntityFromText(remainingText, entity);
      }
    }

    // Enhanced fallback logic: if no money entity found, use fallback parser
    if (!foundMoneyEntity) {
      print('MLKit found entities but no money entity, falling back to regex parser');
      return await _fallbackParser.parseTransaction(text);
    }

    // Fallback to regex if ML Kit didn't find amount
    if (amount == null) {
      final regexResult = _extractAmountWithRegex(text);
      if (regexResult != null) {
        amount = regexResult['amount'] as double?;
        currencyCode = currencyCode ?? regexResult['currency'] as String?;
        if (amount != null) {
          remainingText = _removeAmountFromText(remainingText, amount);
        }
      }
    }

    // Enhanced fallback logic: if amount extraction still fails, use fallback parser
    if (amount == null) {
      print('MLKit found money entity but amount extraction failed, falling back to regex parser');
      return await _fallbackParser.parseTransaction(text);
    }

    // If no currency found, get default currency
    if (currencyCode == null) {
      currencyCode = await _storageService.getDefaultCurrency();
    }

    // Detect transaction type with negative amount information
    final isNegativeAmount = text.trim().startsWith('-');
    final type = _detectTransactionType(text, isNegativeAmount: isNegativeAmount);
    if (type == null) {
      return ParseResult.failed(
        _createFallbackTransaction(text, amount: amount),
        'Could not determine transaction type'
      );
    }

    // Find category using the category finder service
    final categoryId = await _categoryFinder.findCategory(remainingText, type);

    // Create the transaction
    final transaction = Transaction(
      id: _uuid.v4(),
      amount: amount,
      type: type,
      categoryId: categoryId ?? 'other', // Use default category if none found
      date: extractedDate ?? DateTime.now(),
      description: _createDescription(text),
      tags: _extractTags(text),
      currencyCode: currencyCode,
    );

    // Return result indicating if category selection is needed
    if (categoryId == null) {
      return ParseResult.needsCategory(transaction);
    } else {
      return ParseResult.success(transaction);
    }
  }

  /// Learn a category association for future use
  Future<void> learnCategory(String text, String categoryId) async {
    if (_mlKitAvailable) {
      await _categoryFinder.learnCategory(text, categoryId);
    } else {
      await _fallbackParser.learnCategory(text, categoryId);
    }
  }

  /// Parse money entity from ML Kit
  Map<String, dynamic>? _parseMoneyEntity(EntityAnnotation entity) {
    try {
      // Extract the numeric value and currency from the money entity text
      final text = entity.text;
      
      // Try to extract amount
      final numericRegex = RegExp(r'(\d+[.,]?\d*)');
      final match = numericRegex.firstMatch(text);
      double? amount;
      String? currency;
      
      if (match != null) {
        final numericString = match.group(1)!.replaceAll(',', '.');
        amount = double.tryParse(numericString);
      }
      
      // Try to extract currency from the text with context
      currency = _extractCurrencyFromText(text);
      
      if (amount != null) {
        return {
          'amount': amount,
          'currency': currency,
        };
      }
    } catch (e) {
      // Fallback to text parsing
      final cleanText = entity.text.replaceAll(RegExp(r'[^\d.,]'), '').replaceAll(',', '.');
      final amount = double.tryParse(cleanText);
      if (amount != null) {
        return {
          'amount': amount,
          'currency': _extractCurrencyFromText(entity.text),
        };
      }
    }
    return null;
  }

  /// Parse datetime entity from ML Kit
  DateTime? _parseDateTimeEntity(EntityAnnotation entity) {
    try {
      // For now, we'll use the current date as ML Kit entity extraction
      // doesn't directly provide parsed DateTime objects in this version
      return DateTime.now();
    } catch (e) {
      return DateTime.now();
    }
  }

  /// Remove entity text from the remaining text
  String _removeEntityFromText(String text, EntityAnnotation entity) {
    final start = entity.start;
    final end = entity.end;
    
    if (start >= 0 && end <= text.length && end > start) {
      return (text.substring(0, start) + text.substring(end)).trim();
    }
    return text;
  }

  /// Fallback regex amount extraction (from original parser)
  Map<String, dynamic>? _extractAmountWithRegex(String text) {
    final normalizedText = text.toLowerCase();

    bool isNegative = false;
    String processedText = normalizedText;

    if (normalizedText.startsWith('-')) {
      isNegative = true;
      processedText = normalizedText.substring(1).trim();
    }

    final result = _extractPositiveAmount(processedText);
    if (result != null) {
      result['isNegative'] = isNegative;
    }

    return result;
  }

  /// Helper to extract positive amount values from text
  Map<String, dynamic>? _extractPositiveAmount(String text) {
    // Fixed regex to properly handle large numbers without comma issues
    // Simplified to handle any number of digits with optional commas and decimals
    final amountRegex = RegExp(r'(\$|€|£|¥|₹|₽|₩|₱|₫|฿|₺|₪|R\$|S\$|HK\$|A\$|C\$|NZ\$)?\s?(\d+(?:,\d{3})*(?:\.\d{1,2})?)\s?(?:dollars|USD|euros?|EUR|pounds?|GBP|yen|JPY|yuan|CNY|rupees?|INR|rubles?|RUB|won|KRW|pesos?|MXN|PHP|dong|VND|baht|THB|lira|TRY|shekel|ILS|reais?|BRL|SGD|HKD|AUD|CAD|NZD|\$|€|£|¥|₹|₽|₩|₱|₫|฿|₺|₪)?');
    final match = amountRegex.firstMatch(text);

    if (match != null) {
      final currencySymbol = match.group(1);
      final amountString = match.group(2)!.replaceAll(',', ''); // Remove commas for parsing
      final amount = double.tryParse(amountString);

      if (amount != null) {
        String? currency = _extractCurrencyFromText(text);
        if (currency == null && currencySymbol != null) {
          currency = CurrencyUtils.symbolToCurrencyCode(currencySymbol, context: text);
        }

        return {
          'amount': amount,
          'currency': currency,
        };
      }
    }

    return null;
  }

  /// Extract currency information from text
  String? _extractCurrencyFromText(String text) {
    // Check for currency symbols first with context-aware detection
    final symbolRegex = RegExp(r'(\$|€|£|¥|₹|₽|₩|₱|₫|฿|₺|₪|R\$|S\$|HK\$|A\$|C\$|NZ\$)');
    final symbolMatch = symbolRegex.firstMatch(text);
    if (symbolMatch != null) {
      return CurrencyUtils.symbolToCurrencyCode(symbolMatch.group(1)!, context: text);
    }
    
    // Check for currency codes
    final codeRegex = RegExp(r'\b(USD|EUR|GBP|JPY|CNY|INR|KRW|MXN|PHP|VND|THB|TRY|ILS|BRL|SGD|HKD|AUD|CAD|NZD|RUB)\b', caseSensitive: false);
    final codeMatch = codeRegex.firstMatch(text);
    if (codeMatch != null) {
      return codeMatch.group(1)!.toUpperCase();
    }
    
    // Check for currency names
    final nameMap = {
      'dollars?': 'USD',
      'euros?': 'EUR',
      'pounds?': 'GBP',
      'yen': 'JPY',
      'yuan': 'CNY',
      'rupees?': 'INR',
      'won': 'KRW',
      'pesos?': 'MXN',
      'dong': 'VND',
      'baht': 'THB',
      'lira': 'TRY',
      'shekel': 'ILS',
      'reais?': 'BRL',
      'rubles?': 'RUB',
    };
    
    for (final entry in nameMap.entries) {
      if (RegExp(entry.key, caseSensitive: false).hasMatch(text)) {
        return entry.value;
      }
    }
    
    return null;
  }

  /// Remove amount from text for better category detection
  String _removeAmountFromText(String text, double amount) {
    final amountStr = amount.toString();
    return text
        .replaceAll(RegExp(r'\$?\s?' + RegExp.escape(amountStr) + r'\s?(?:dollars|USD|\$|€|EUR|£|GBP)?'), '')
        .trim();
  }

  /// Detect transaction type (from original parser)
  TransactionType? _detectTransactionType(String text, {bool isNegativeAmount = false}) {
    final normalizedText = text.toLowerCase().trim();

    if (RegExp(r'^\s*-').hasMatch(normalizedText) || isNegativeAmount) {
      return TransactionType.expense;
    }
    
    if (RegExp(r'(spent|paid|bought|purchased|expense|pay|payment|cost|spent on|paid for|charge|bought for|dinner|lunch|breakfast|meal|food|coffee|restaurant|groceries|shopping|gas|fuel)')
        .hasMatch(normalizedText)) {
      return TransactionType.expense;
    }
    
    if (RegExp(r'(received|earned|income|salary|payment received|got paid|got money|earned from|money from|receive|selling|sold|gift|bonus|dividend|interest|return|gain|profit|reward)')
        .hasMatch(normalizedText)) {
      return TransactionType.income;
    }
    
    if (RegExp(r'(borrowed|lent|loan|debt|credit|lend|borrowed from|lent to)')
        .hasMatch(normalizedText)) {
      return TransactionType.loan;
    }
    
    if (RegExp(r'\bfor\b').hasMatch(normalizedText) && 
        !RegExp(r'(received|earned|income|salary|payment received|got paid|got money|earned from|money from|receive|selling|sold|gift).*?\bfor\b').hasMatch(normalizedText)) {
      return TransactionType.expense;
    }
    
    if (RegExp(r'[$€£¥]').hasMatch(normalizedText)) {
      return TransactionType.expense;
    }
    
    return null;
  }

  /// Create description from text
  String _createDescription(String text) {
    return text.trim();
  }

  /// Extract tags from text
  List<String> _extractTags(String text) {
    final tags = <String>[];
    final hashtagRegex = RegExp(r'#(\w+)');
    final matches = hashtagRegex.allMatches(text);
    
    for (final match in matches) {
      final tag = match.group(1);
      if (tag != null) {
        tags.add(tag);
      }
    }
    
    return tags;
  }

  /// Create fallback transaction when parsing fails
  Transaction _createFallbackTransaction(String text, {double? amount}) {
    return Transaction(
      id: _uuid.v4(),
      amount: amount ?? 0.0,
      type: TransactionType.expense,
      categoryId: 'other',
      date: DateTime.now(),
      description: text.trim(),
      tags: _extractTags(text),
      currencyCode: 'USD',
    );
  }

  /// Dispose resources
  void dispose() {
    _entityExtractor?.close();
  }
}
