/// Multilingual keyword mappings for category detection
/// Maps category IDs to lists of keywords in English, Spanish, and German
const Map<String, List<String>> categoryKeywords = {
  // Food & Drink
  'food': [
    // English
    'food', 'restaurant', 'cafe', 'coffee', 'lunch', 'dinner', 'breakfast',
    'pizza', 'burger', 'sandwich', 'meal', 'eat', 'drink', 'bar', 'pub',
    'snack', 'grocery', 'supermarket', 'kitchen', 'cooking', 'recipe',
    'mcdonalds', 'starbucks', 'subway', 'kfc', 'dominos', 'taco bell',
    'wendys', 'chipotle', 'panera', 'dunkin', 'chick-fil-a',
    // Spanish
    'comida', 'restaurante', 'almuerzo', 'cena', 'desayuno', 'bebida',
    'supermercado', 'cocina', 'comer', 'beber', 'cafeteria', 'bar',
    // German
    'essen', 'restaurant', 'mittagessen', 'abendessen', 'frühstück',
    'trinken', 'supermarkt', 'küche', 'café', 'bar'
  ],

  // Transportation
  'transport': [
    // English
    'transport', 'taxi', 'uber', 'lyft', 'bus', 'train', 'subway', 'metro',
    'gas', 'fuel', 'petrol', 'parking', 'toll', 'car', 'vehicle',
    'airline', 'flight', 'airport', 'ticket', 'travel', 'trip',
    'bike', 'bicycle', 'scooter', 'motorcycle', 'boat', 'ferry',
    // Spanish
    'transporte', 'taxi', 'autobús', 'tren', 'metro', 'gasolina',
    'combustible', 'estacionamiento', 'peaje', 'coche', 'viaje',
    'vuelo', 'aeropuerto', 'billete', 'bicicleta',
    // German
    'transport', 'taxi', 'bus', 'zug', 'u-bahn', 'benzin', 'kraftstoff',
    'parkplatz', 'maut', 'auto', 'fahrzeug', 'flug', 'flughafen',
    'ticket', 'reise', 'fahrrad'
  ],

  // Shopping
  'shopping': [
    // English
    'shopping', 'store', 'mall', 'market', 'retail', 'purchase', 'buy',
    'clothes', 'clothing', 'fashion', 'shoes', 'accessories', 'jewelry',
    'electronics', 'gadget', 'phone', 'computer', 'laptop', 'tablet',
    'amazon', 'ebay', 'walmart', 'target', 'costco', 'best buy',
    'apple store', 'nike', 'adidas', 'zara', 'h&m',
    // Spanish
    'compras', 'tienda', 'centro comercial', 'mercado', 'comprar',
    'ropa', 'zapatos', 'accesorios', 'joyería', 'electrónicos',
    'teléfono', 'computadora', 'portátil',
    // German
    'einkaufen', 'geschäft', 'einkaufszentrum', 'markt', 'kaufen',
    'kleidung', 'schuhe', 'accessoires', 'schmuck', 'elektronik',
    'telefon', 'computer', 'laptop'
  ],

  // Utilities
  'utilities': [
    // English
    'electric', 'electricity', 'gas', 'water', 'internet', 'phone',
    'cable', 'tv', 'streaming', 'netflix', 'spotify', 'subscription',
    'utility', 'bill', 'payment', 'service', 'provider',
    'verizon', 'att', 'comcast', 'spectrum', 'hulu', 'disney+',
    // Spanish
    'electricidad', 'agua', 'internet', 'teléfono', 'cable',
    'servicios', 'factura', 'pago', 'proveedor', 'suscripción',
    // German
    'strom', 'elektrizität', 'wasser', 'internet', 'telefon',
    'kabel', 'rechnung', 'zahlung', 'anbieter', 'abonnement'
  ],

  // Entertainment
  'entertainment': [
    // English
    'movie', 'cinema', 'theater', 'concert', 'music', 'game', 'gaming',
    'entertainment', 'fun', 'party', 'club', 'festival', 'event',
    'sport', 'gym', 'fitness', 'recreation', 'hobby', 'leisure',
    'netflix', 'spotify', 'xbox', 'playstation', 'steam',
    // Spanish
    'película', 'cine', 'teatro', 'concierto', 'música', 'juego',
    'entretenimiento', 'diversión', 'fiesta', 'club', 'festival',
    'deporte', 'gimnasio', 'recreación', 'hobby',
    // German
    'film', 'kino', 'theater', 'konzert', 'musik', 'spiel',
    'unterhaltung', 'spaß', 'party', 'club', 'festival',
    'sport', 'fitnessstudio', 'erholung', 'hobby'
  ],

  // Health & Medical
  'health': [
    // English
    'health', 'medical', 'doctor', 'hospital', 'pharmacy', 'medicine',
    'prescription', 'dental', 'dentist', 'clinic', 'treatment',
    'insurance', 'wellness', 'fitness', 'vitamin', 'supplement',
    'cvs', 'walgreens', 'rite aid', 'urgent care',
    // Spanish
    'salud', 'médico', 'hospital', 'farmacia', 'medicina',
    'receta', 'dental', 'dentista', 'clínica', 'tratamiento',
    'seguro', 'bienestar', 'vitamina', 'suplemento',
    // German
    'gesundheit', 'medizinisch', 'arzt', 'krankenhaus', 'apotheke',
    'medizin', 'rezept', 'zahnarzt', 'klinik', 'behandlung',
    'versicherung', 'wellness', 'vitamin', 'nahrungsergänzung'
  ],

  // Education
  'education': [
    // English
    'school', 'university', 'college', 'education', 'tuition', 'course',
    'class', 'book', 'textbook', 'supplies', 'learning', 'study',
    'teacher', 'professor', 'student', 'academic', 'training',
    // Spanish
    'escuela', 'universidad', 'colegio', 'educación', 'matrícula',
    'curso', 'clase', 'libro', 'suministros', 'aprendizaje',
    'maestro', 'profesor', 'estudiante', 'entrenamiento',
    // German
    'schule', 'universität', 'hochschule', 'bildung', 'studiengebühren',
    'kurs', 'klasse', 'buch', 'lehrbuch', 'lernen', 'studium',
    'lehrer', 'professor', 'student', 'ausbildung'
  ],

  // Home & Garden
  'home': [
    // English
    'home', 'house', 'rent', 'mortgage', 'furniture', 'decoration',
    'garden', 'tools', 'repair', 'maintenance', 'cleaning', 'supplies',
    'ikea', 'home depot', 'lowes', 'bed bath beyond',
    // Spanish
    'casa', 'hogar', 'alquiler', 'hipoteca', 'muebles', 'decoración',
    'jardín', 'herramientas', 'reparación', 'mantenimiento', 'limpieza',
    // German
    'haus', 'zuhause', 'miete', 'hypothek', 'möbel', 'dekoration',
    'garten', 'werkzeuge', 'reparatur', 'wartung', 'reinigung'
  ],

  // Personal Care
  'personal': [
    // English
    'beauty', 'cosmetics', 'haircut', 'salon', 'spa', 'massage',
    'personal care', 'hygiene', 'skincare', 'makeup', 'perfume',
    'barber', 'nail', 'manicure', 'pedicure',
    // Spanish
    'belleza', 'cosméticos', 'corte de pelo', 'salón', 'cuidado personal',
    'higiene', 'maquillaje', 'perfume', 'barbero', 'uñas',
    // German
    'schönheit', 'kosmetik', 'haarschnitt', 'salon', 'körperpflege',
    'hygiene', 'hautpflege', 'makeup', 'parfüm', 'friseur', 'nagel'
  ],

  // Business & Professional
  'business': [
    // English
    'business', 'office', 'supplies', 'professional', 'meeting',
    'conference', 'software', 'service', 'consulting', 'legal',
    'accounting', 'tax', 'bank', 'fee', 'commission',
    // Spanish
    'negocio', 'oficina', 'suministros', 'profesional', 'reunión',
    'conferencia', 'software', 'servicio', 'consultoría', 'legal',
    'contabilidad', 'impuesto', 'banco', 'tarifa', 'comisión',
    // German
    'geschäft', 'büro', 'büromaterial', 'professionell', 'besprechung',
    'konferenz', 'software', 'service', 'beratung', 'rechtlich',
    'buchhaltung', 'steuer', 'bank', 'gebühr', 'provision'
  ]
};

/// Utility function to find category by keywords
String? findCategoryByKeywords(String text) {
  if (text.isEmpty) return null;
  
  final normalizedText = text.toLowerCase().trim();
  
  // Split text into words for matching
  final words = normalizedText.split(RegExp(r'\s+'));
  
  // Score each category based on keyword matches
  final categoryScores = <String, int>{};
  
  for (final entry in categoryKeywords.entries) {
    final categoryId = entry.key;
    final keywords = entry.value;
    int score = 0;
    
    // Check if any keyword matches
    for (final keyword in keywords) {
      final keywordLower = keyword.toLowerCase();
      
      // Exact word match gets highest score
      if (words.contains(keywordLower)) {
        // Give extra points for longer/more specific keywords
        score += 10 + (keywordLower.length > 5 ? 3 : 0);
        continue;
      }
      
      // Brand/proper noun exact matches get very high score
      if (keywordLower.length > 3 && _isBrandName(keywordLower)) {
        if (normalizedText.contains(keywordLower)) {
          score += 15;
          continue;
        }
      }
      
      // Partial word match gets medium score
      bool partialMatch = false;
      for (final word in words) {
        if (word.length >= 3 && keywordLower.length >= 3) {
          if (word.contains(keywordLower) || keywordLower.contains(word)) {
            score += 6;
            partialMatch = true;
            break;
          }
        }
      }
      
      if (partialMatch) continue;
      
      // Full text contains keyword gets low score
      if (normalizedText.contains(keywordLower) && keywordLower.length >= 3) {
        score += 2;
      }
    }
    
    if (score > 0) {
      categoryScores[categoryId] = score;
    }
  }
  
  // Return the category with the highest score, but only if it has a significant score
  if (categoryScores.isEmpty) return null;
  
  final bestCategory = categoryScores.entries
      .reduce((a, b) => a.value > b.value ? a : b);
  
  // Only return a match if the score is significant enough
  if (bestCategory.value < 6) return null;
  
  // Check if there's a clear winner (avoid ambiguous matches)
  final sortedScores = categoryScores.entries.toList()
    ..sort((a, b) => b.value.compareTo(a.value));
  
  if (sortedScores.length > 1) {
    final best = sortedScores[0];
    final second = sortedScores[1];
    
    // If the difference is small and both have generic keywords, we're not confident
    if (best.value < second.value + 4 && best.value < 10) {
      return null;
    }
  }
  
  return bestCategory.key;
}

/// Check if a keyword is a brand name or proper noun
bool _isBrandName(String keyword) {
  final brandKeywords = [
    'mcdonalds', 'starbucks', 'subway', 'kfc', 'dominos', 'taco bell',
    'wendys', 'chipotle', 'panera', 'dunkin', 'chick-fil-a',
    'amazon', 'ebay', 'walmart', 'target', 'costco', 'best buy',
    'apple store', 'nike', 'adidas', 'zara', 'h&m', 'uber', 'lyft'
  ];
  return brandKeywords.contains(keyword);
}

/// Get keywords for a specific category
List<String> getKeywordsForCategory(String categoryId) {
  return categoryKeywords[categoryId] ?? [];
}

/// Get all available category IDs that have keywords
List<String> getAllCategoryIds() {
  return categoryKeywords.keys.toList();
}
