import 'package:flutter_test/flutter_test.dart';
import '../../../lib/services/parser/mlkit_parser_service.dart';
import '../../../lib/models/transaction_model.dart';
import '../../mocks/mock_storage_service.dart';
import '../../test_data/sample_transactions.dart';

void main() {
  group('MlKitParserService Tests', () {
    late MockStorageService mockStorage;

    setUp(() async {
      mockStorage = MockStorageService();
      await mockStorage.init();
    });

    tearDown(() {
      // Reset singleton instance for clean tests
      // Note: In a real implementation, we might need a way to reset the singleton
    });

    group('Service Initialization', () {
      test('should create singleton instance', () async {
        final instance1 = await MlKitParserService.getInstance(mockStorage);
        final instance2 = await MlKitParserService.getInstance(mockStorage);
        
        expect(identical(instance1, instance2), isTrue);
      });

      test('should initialize with MLKit available', () async {
        final service = await MlKitParserService.getInstance(mockStorage);
        expect(service, isNotNull);
      });

      test('should handle MLKit initialization failure gracefully', () async {
        // This test depends on the ability to simulate MLKit initialization failure
        // In a real test, we would mock the MLKit initialization to fail
        final service = await MlKitParserService.getInstance(mockStorage);
        expect(service, isNotNull);
      });
    });

    group('Transaction Parsing with MLKit', () {
      test('should parse simple expense transactions', () async {
        final service = await MlKitParserService.getInstance(mockStorage);
        final testCases = SampleTransactions.simpleExpenses;
        
        for (final entry in testCases.entries) {
          final testData = entry.value;
          final text = testData['text'] as String;
          final expectedAmount = testData['expected_amount'] as double;
          final expectedType = testData['expected_type'] as TransactionType;

          final result = await service.parseTransaction(text);
          
          // Should either succeed or fall back gracefully
          expect(result, isNotNull);
          expect(result.transaction.amount, greaterThanOrEqualTo(0.0));
          
          // If parsing is successful, check expected values
          if (result.isSuccess || result.requiresUserInput) {
            expect(result.transaction.amount, equals(expectedAmount), reason: 'Amount mismatch for: $text');
            expect(result.transaction.type, equals(expectedType), reason: 'Type mismatch for: $text');
          }
        }
      });

      test('should parse income transactions', () async {
        final service = await MlKitParserService.getInstance(mockStorage);
        final testCases = SampleTransactions.incomeTransactions;
        
        for (final entry in testCases.entries) {
          final testData = entry.value;
          final text = testData['text'] as String;
          final expectedAmount = testData['expected_amount'] as double;

          final result = await service.parseTransaction(text);
          
          expect(result, isNotNull);
          if (result.isSuccess || result.requiresUserInput) {
            expect(result.transaction.amount, equals(expectedAmount), reason: 'Amount mismatch for: $text');
            expect(result.transaction.type, equals(TransactionType.income), reason: 'Type mismatch for: $text');
          }
        }
      });

      test('should parse complex transactions with tags', () async {
        final service = await MlKitParserService.getInstance(mockStorage);
        final testCases = SampleTransactions.complexTransactions;
        
        for (final entry in testCases.entries) {
          final testData = entry.value;
          final text = testData['text'] as String;
          final expectedTags = testData['expected_tags'] as List<dynamic>?;

          final result = await service.parseTransaction(text);
          
          expect(result, isNotNull);
          if (expectedTags != null && (result.isSuccess || result.requiresUserInput)) {
            for (final tag in expectedTags) {
              expect(result.transaction.tags, contains(tag), reason: 'Missing tag $tag for: $text');
            }
          }
        }
      });
    });

    group('Fallback Behavior', () {
      test('should fall back to regex parsing when MLKit fails', () async {
        final service = await MlKitParserService.getInstance(mockStorage);
        
        // Test with various transaction texts that should work with fallback
        final testTexts = [
          'Spent \$25.50 on coffee',
          'Received €100 salary',
          'Lent £50 to friend',
        ];

        for (final text in testTexts) {
          final result = await service.parseTransaction(text);
          
          // Should get a result either from MLKit or fallback
          expect(result, isNotNull);
          expect(result.transaction.amount, greaterThan(0.0));
        }
      });

      test('should handle texts that both MLKit and regex can\'t parse', () async {
        final service = await MlKitParserService.getInstance(mockStorage);
        final malformedInputs = SampleTransactions.malformedInputs;
        
        for (final entry in malformedInputs.entries) {
          final testData = entry.value;
          final text = testData['text'] as String;
          final shouldFail = testData['should_fail'] as bool? ?? false;

          final result = await service.parseTransaction(text);
          
          if (shouldFail) {
            expect(result.hasError, isTrue, reason: 'Should fail for: $text');
          } else {
            // If it shouldn't fail, we should get some result
            expect(result, isNotNull);
          }
        }
      });
    });

    group('Currency Handling', () {
      test('should detect various currency formats', () async {
        final service = await MlKitParserService.getInstance(mockStorage);
        final currencyTests = SampleTransactions.currencyVariations;
        
        for (final entry in currencyTests.entries) {
          final testData = entry.value;
          final text = testData['text'] as String;
          final expectedCurrency = testData['expected_currency'] as String;

          final result = await service.parseTransaction(text);
          
          if (result.isSuccess || result.requiresUserInput) {
            expect(result.transaction.currencyCode, equals(expectedCurrency), reason: 'Currency mismatch for: $text');
          }
        }
      });

      test('should use default currency when none detected', () async {
        await mockStorage.saveDefaultCurrency('EUR');
        final service = await MlKitParserService.getInstance(mockStorage);
        
        const text = 'Spent 25.50 on something';
        final result = await service.parseTransaction(text);
        
        if (result.isSuccess || result.requiresUserInput) {
          expect(result.transaction.currencyCode, equals('EUR'));
        }
      });
    });

    group('Category Learning', () {
      test('should learn and apply category associations', () async {
        final service = await MlKitParserService.getInstance(mockStorage);
        
        const text = 'Coffee at my local cafe';
        const categoryId = 'food';

        // Learn the association
        await service.learnCategory(text, categoryId);

        // Parse the same text again
        final result = await service.parseTransaction(text);
        
        if (result.isSuccess) {
          expect(result.transaction.categoryId, equals(categoryId));
        }
      });

      test('should prioritize learned categories over automatic detection', () async {
        final service = await MlKitParserService.getInstance(mockStorage);
        
        const text = 'Coffee shop \$5.50';
        const learnedCategoryId = 'beverages';

        // Learn a specific category
        await service.learnCategory(text, learnedCategoryId);

        // Parse again - should use learned category
        final result = await service.parseTransaction(text);
        
        if (result.isSuccess) {
          expect(result.transaction.categoryId, equals(learnedCategoryId));
        }
      });
    });

    group('Edge Cases', () {
      test('should handle empty input', () async {
        final service = await MlKitParserService.getInstance(mockStorage);
        
        final result = await service.parseTransaction('');
        expect(result.hasError, isTrue);
      });

      test('should handle whitespace-only input', () async {
        final service = await MlKitParserService.getInstance(mockStorage);
        
        final result = await service.parseTransaction('   \n\t  ');
        expect(result.hasError, isTrue);
      });

      test('should handle very long text', () async {
        final service = await MlKitParserService.getInstance(mockStorage);
        
        final longText = 'This is a very long transaction description that contains a lot of text and details about the purchase including the amount of \$25.50 spent on coffee at a local cafe with friends on a sunny afternoon during the weekend when everyone was relaxing and enjoying their time together';
        
        final result = await service.parseTransaction(longText);
        
        // Should handle long text gracefully
        expect(result, isNotNull);
        if (result.isSuccess || result.requiresUserInput) {
          expect(result.transaction.amount, equals(25.50));
        }
      });

      test('should handle special characters and unicode', () async {
        final service = await MlKitParserService.getInstance(mockStorage);
        
        const text = 'Café & Restaurant! €25.50 🍽️ #food';
        final result = await service.parseTransaction(text);
        
        expect(result, isNotNull);
        if (result.isSuccess || result.requiresUserInput) {
          expect(result.transaction.amount, equals(25.50));
          expect(result.transaction.currencyCode, equals('EUR'));
        }
      });

      test('should handle multiple amounts in text', () async {
        final service = await MlKitParserService.getInstance(mockStorage);
        
        const text = 'Spent \$10 on coffee and \$15 on lunch';
        final result = await service.parseTransaction(text);
        
        expect(result, isNotNull);
        if (result.isSuccess || result.requiresUserInput) {
          // Should pick one of the amounts (typically the first)
          expect([10.0, 15.0], contains(result.transaction.amount));
        }
      });
    });

    group('Error Handling and Recovery', () {
      test('should handle parsing errors gracefully', () async {
        final service = await MlKitParserService.getInstance(mockStorage);
        
        // Test various problematic inputs
        final problematicInputs = [
          'invalid input with no clear structure',
          '###@@@%%% weird characters',
          'amount without numbers',
          'very confusing transaction description',
        ];

        for (final text in problematicInputs) {
          expect(() async {
            await service.parseTransaction(text);
          }, returnsNormally, reason: 'Should not throw for: $text');
        }
      });

      test('should provide meaningful error messages', () async {
        final service = await MlKitParserService.getInstance(mockStorage);
        
        final result = await service.parseTransaction('no amount here');
        
        if (result.hasError) {
          expect(result.error, isNotNull);
          expect(result.error!.length, greaterThan(0));
        }
      });
    });

    group('Transaction ID and Metadata', () {
      test('should generate unique transaction IDs', () async {
        final service = await MlKitParserService.getInstance(mockStorage);
        
        const text = 'Coffee \$5.50';
        final result1 = await service.parseTransaction(text);
        final result2 = await service.parseTransaction(text);
        
        if (result1.isSuccess && result2.isSuccess) {
          expect(result1.transaction.id, isNot(equals(result2.transaction.id)));
        }
      });

      test('should set appropriate timestamps', () async {
        final service = await MlKitParserService.getInstance(mockStorage);
        
        const text = 'Coffee \$5.50';
        final beforeParse = DateTime.now();
        final result = await service.parseTransaction(text);
        final afterParse = DateTime.now();
        
        if (result.isSuccess || result.requiresUserInput) {
          expect(result.transaction.date.isAfter(beforeParse.subtract(const Duration(seconds: 1))), isTrue);
          expect(result.transaction.date.isBefore(afterParse.add(const Duration(seconds: 1))), isTrue);
        }
      });

      test('should create meaningful descriptions', () async {
        final service = await MlKitParserService.getInstance(mockStorage);
        
        const text = 'Coffee shop \$5.50 downtown location';
        final result = await service.parseTransaction(text);
        
        if (result.isSuccess || result.requiresUserInput) {
          expect(result.transaction.description, isNotEmpty);
          expect(result.transaction.description.toLowerCase(), contains('coffee'));
        }
      });
    });

    group('Performance Tests', () {
      test('should handle multiple parsing requests efficiently', () async {
        final service = await MlKitParserService.getInstance(mockStorage);
        
        final testTexts = [
          'Coffee \$5.50',
          'Gas \$40.00',
          'Groceries \$85.30',
          'Dinner \$45.99',
          'Movie \$12.50',
        ];

        final stopwatch = Stopwatch()..start();

        for (int i = 0; i < 50; i++) {
          for (final text in testTexts) {
            await service.parseTransaction(text);
          }
        }

        stopwatch.stop();
        
        // Should complete in reasonable time (this is generous to account for MLKit overhead)
        expect(stopwatch.elapsedMilliseconds, lessThan(10000));
      });

      test('should handle singleton access efficiently', () async {
        final stopwatch = Stopwatch()..start();

        for (int i = 0; i < 100; i++) {
          await MlKitParserService.getInstance(mockStorage);
        }

        stopwatch.stop();
        expect(stopwatch.elapsedMilliseconds, lessThan(1000));
      });
    });

    group('Real-world Integration', () {
      test('should parse real-world transaction examples', () async {
        final service = await MlKitParserService.getInstance(mockStorage);
        final realWorldCases = SampleTransactions.realWorldExamples;
        
        for (final entry in realWorldCases.entries) {
          final testData = entry.value;
          final text = testData['text'] as String;
          final expectedAmount = testData['expected_amount'] as double;

          final result = await service.parseTransaction(text);
          
          expect(result, isNotNull);
          
          // Should either succeed or require user input (not fail completely)
          expect(result.isSuccess || result.requiresUserInput, isTrue, 
                 reason: 'Should handle real-world case: $text');
          
          if (result.isSuccess || result.requiresUserInput) {
            expect(result.transaction.amount, equals(expectedAmount), 
                   reason: 'Amount mismatch for real-world case: $text');
          }
        }
      });
    });
  });
}
