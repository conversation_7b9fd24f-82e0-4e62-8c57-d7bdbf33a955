import 'package:flutter_test/flutter_test.dart';
import '../../../lib/services/parser/fallback_parser_service.dart';
import '../../../lib/models/transaction_model.dart';
import '../../mocks/mock_storage_service.dart';
import '../../test_data/sample_transactions.dart';

void main() {
  group('FallbackParserService Tests', () {
    late FallbackParserService fallbackParser;
    late MockStorageService mockStorage;

    setUp(() async {
      mockStorage = MockStorageService();
      await mockStorage.init();
      fallbackParser = FallbackParserService(mockStorage);
    });

    group('Basic Transaction Parsing', () {
      test('should parse simple expense transactions', () async {
        final testCases = SampleTransactions.simpleExpenses;
        
        for (final entry in testCases.entries) {
          final testData = entry.value;
          final text = testData['text'] as String;
          final expectedAmount = testData['expected_amount'] as double;
          final expectedCurrency = testData['expected_currency'] as String;
          final expectedType = testData['expected_type'] as TransactionType;

          final result = await fallbackParser.parseTransaction(text);
          
          expect(result.isSuccess, isTrue, reason: 'Failed for: $text');
          expect(result.transaction.amount, equals(expectedAmount), reason: 'Amount mismatch for: $text');
          expect(result.transaction.currencyCode, equals(expectedCurrency), reason: 'Currency mismatch for: $text');
          expect(result.transaction.type, equals(expectedType), reason: 'Type mismatch for: $text');
        }
      });

      test('should parse income transactions', () async {
        final testCases = SampleTransactions.incomeTransactions;
        
        for (final entry in testCases.entries) {
          final testData = entry.value;
          final text = testData['text'] as String;
          final expectedAmount = testData['expected_amount'] as double;
          final expectedType = testData['expected_type'] as TransactionType;

          final result = await fallbackParser.parseTransaction(text);
          
          expect(result.transaction.amount, equals(expectedAmount), reason: 'Amount mismatch for: $text');
          expect(result.transaction.type, equals(expectedType), reason: 'Type mismatch for: $text');
        }
      });

      test('should parse loan transactions', () async {
        final testCases = SampleTransactions.loanTransactions;
        
        for (final entry in testCases.entries) {
          final testData = entry.value;
          final text = testData['text'] as String;
          final expectedAmount = testData['expected_amount'] as double;
          final expectedType = testData['expected_type'] as TransactionType;

          final result = await fallbackParser.parseTransaction(text);
          
          expect(result.transaction.amount, equals(expectedAmount), reason: 'Amount mismatch for: $text');
          expect(result.transaction.type, equals(expectedType), reason: 'Type mismatch for: $text');
        }
      });
    });

    group('Amount Extraction', () {
      test('should extract amounts with different currency symbols', () async {
        final testCases = {
          '\$25.50': {'amount': 25.50, 'currency': 'USD'},
          '€150.75': {'amount': 150.75, 'currency': 'EUR'},
          '£45.99': {'amount': 45.99, 'currency': 'GBP'},
          '¥1000': {'amount': 1000.0, 'currency': 'JPY'},
          '₹2500.50': {'amount': 2500.50, 'currency': 'INR'},
          '₩5000': {'amount': 5000.0, 'currency': 'KRW'},
        };

        for (final entry in testCases.entries) {
          final text = 'Spent ${entry.key} on shopping';
          final expected = entry.value;

          final result = await fallbackParser.parseTransaction(text);
          
          expect(result.transaction.amount, equals(expected['amount']), reason: 'Amount mismatch for: $text');
          expect(result.transaction.currencyCode, equals(expected['currency']), reason: 'Currency mismatch for: $text');
        }
      });

      test('should handle amounts with various formats', () async {
        final testCases = {
          'Spent \$5': 5.0,
          'Paid \$10.99': 10.99,
          'Cost \$100.00': 100.0,
          'Price \$1,234.56': 1234.56,
          'Total \$0.01': 0.01,
          'Amount \$999.99': 999.99,
        };

        for (final entry in testCases.entries) {
          final result = await fallbackParser.parseTransaction(entry.key);
          expect(result.transaction.amount, equals(entry.value), reason: 'Failed for: ${entry.key}');
        }
      });

      test('should handle whole number amounts', () async {
        final testCases = {
          'Spent \$20': 20.0,
          'Paid €50': 50.0,
          'Cost £100': 100.0,
        };

        for (final entry in testCases.entries) {
          final result = await fallbackParser.parseTransaction(entry.key);
          expect(result.transaction.amount, equals(entry.value), reason: 'Failed for: ${entry.key}');
        }
      });

      test('should use default currency when none specified', () async {
        await mockStorage.saveDefaultCurrency('EUR');
        
        // Create new parser instance to pick up the default currency
        final parser = FallbackParserService(mockStorage);
        
        const text = 'Spent 25.50 on coffee';
        final result = await parser.parseTransaction(text);
        
        expect(result.transaction.amount, equals(25.50));
        expect(result.transaction.currencyCode, equals('EUR'));
      });
    });

    group('Transaction Type Detection', () {
      test('should detect expense transactions', () async {
        final expenseTexts = [
          'spent \$25 on coffee',
          'paid \$50 for dinner',
          'bought \$30 groceries',
          'cost \$100 for repairs',
          'expense of \$75',
          'charge \$15 for parking',
        ];

        for (final text in expenseTexts) {
          final result = await fallbackParser.parseTransaction(text);
          expect(result.transaction.type, equals(TransactionType.expense), reason: 'Failed for: $text');
        }
      });

      test('should detect income transactions', () async {
        final incomeTexts = [
          'received \$1000 salary',
          'earned \$500 from freelance',
          'income of \$2000',
          'bonus \$300 from work',
          'refund \$50 from store',
          'cashback \$25',
        ];

        for (final text in incomeTexts) {
          final result = await fallbackParser.parseTransaction(text);
          expect(result.transaction.type, equals(TransactionType.income), reason: 'Failed for: $text');
        }
      });

      test('should detect loan transactions', () async {
        final loanTexts = [
          'lent \$200 to friend',
          'borrowed \$500 from bank',
          'loan of \$1000',
          'lend \$150 to colleague',
          'borrow \$300 for emergency',
        ];

        for (final text in loanTexts) {
          final result = await fallbackParser.parseTransaction(text);
          expect(result.transaction.type, equals(TransactionType.loan), reason: 'Failed for: $text');
        }
      });
    });

    group('Currency Detection', () {
      test('should detect currency from symbols', () async {
        final currencyTests = SampleTransactions.currencyVariations;
        
        for (final entry in currencyTests.entries) {
          final testData = entry.value;
          final text = testData['text'] as String;
          final expectedCurrency = testData['expected_currency'] as String;

          final result = await fallbackParser.parseTransaction(text);
          expect(result.transaction.currencyCode, equals(expectedCurrency), reason: 'Failed for: $text');
        }
      });

      test('should handle currency codes in text', () async {
        final testCases = {
          'Spent 25.50 USD on coffee': 'USD',
          'Paid 100 EUR for dinner': 'EUR',
          'Cost 50 GBP for transport': 'GBP',
          'Price 1000 JPY for lunch': 'JPY',
        };

        for (final entry in testCases.entries) {
          final result = await fallbackParser.parseTransaction(entry.key);
          expect(result.transaction.currencyCode, equals(entry.value), reason: 'Failed for: ${entry.key}');
        }
      });

      test('should handle currency names in text', () async {
        final testCases = {
          'Spent 25 dollars on coffee': 'USD',
          'Paid 100 euros for dinner': 'EUR',
          'Cost 50 pounds for transport': 'GBP',
          'Price 1000 yen for lunch': 'JPY',
        };

        for (final entry in testCases.entries) {
          final result = await fallbackParser.parseTransaction(entry.key);
          expect(result.transaction.currencyCode, equals(entry.value), reason: 'Failed for: ${entry.key}');
        }
      });
    });

    group('Tag Extraction', () {
      test('should extract hashtags from text', () async {
        final testCases = SampleTransactions.complexTransactions;
        
        for (final entry in testCases.entries) {
          final testData = entry.value;
          if (testData.containsKey('expected_tags')) {
            final text = testData['text'] as String;
            final expectedTags = testData['expected_tags'] as List<dynamic>;

            final result = await fallbackParser.parseTransaction(text);
            
            for (final tag in expectedTags) {
              expect(result.transaction.tags, contains(tag), reason: 'Missing tag $tag for: $text');
            }
          }
        }
      });

      test('should handle multiple hashtags', () async {
        const text = 'Coffee \$5 #morning #coffee #caffeine #daily';
        final result = await fallbackParser.parseTransaction(text);
        
        expect(result.transaction.tags, contains('morning'));
        expect(result.transaction.tags, contains('coffee'));
        expect(result.transaction.tags, contains('caffeine'));
        expect(result.transaction.tags, contains('daily'));
      });

      test('should handle text without hashtags', () async {
        const text = 'Coffee shop \$5.50';
        final result = await fallbackParser.parseTransaction(text);
        
        expect(result.transaction.tags, isEmpty);
      });
    });

    group('Description Generation', () {
      test('should create meaningful descriptions', () async {
        final testCases = {
          'Spent \$25 on coffee at Starbucks': 'coffee at starbucks',
          'Paid €50 for dinner at restaurant': 'dinner at restaurant',
          'Bought \$30 groceries from store': 'groceries from store',
          'Gas station \$40 fill up': 'gas station fill up',
        };

        for (final entry in testCases.entries) {
          final result = await fallbackParser.parseTransaction(entry.key);
          expect(result.transaction.description.toLowerCase(), contains(entry.value.toLowerCase()), 
                 reason: 'Description mismatch for: ${entry.key}');
        }
      });

      test('should remove hashtags from description', () async {
        const text = 'Coffee \$5 #morning #coffee';
        final result = await fallbackParser.parseTransaction(text);
        
        expect(result.transaction.description, isNot(contains('#morning')));
        expect(result.transaction.description, isNot(contains('#coffee')));
        expect(result.transaction.description.toLowerCase(), contains('coffee'));
      });
    });

    group('Category Integration', () {
      test('should find categories for recognized keywords', () async {
        final testCases = {
          'Coffee \$5 at Starbucks': 'food',
          'Gas \$40 at station': 'transport',
          'Shopping \$100 at mall': 'shopping',
        };

        for (final entry in testCases.entries) {
          final result = await fallbackParser.parseTransaction(entry.key);
          expect(result.transaction.categoryId, equals(entry.value), reason: 'Category mismatch for: ${entry.key}');
        }
      });

      test('should require category selection when none found', () async {
        const text = 'Random expense \$50 for unknown vendor';
        final result = await fallbackParser.parseTransaction(text);
        
        // Should need category selection when no category is found
        expect(result.requiresUserInput, isTrue);
      });
    });

    group('Error Handling', () {
      test('should handle malformed inputs gracefully', () async {
        final malformedInputs = SampleTransactions.malformedInputs;
        
        for (final entry in malformedInputs.entries) {
          final testData = entry.value;
          final text = testData['text'] as String;
          final shouldFail = testData['should_fail'] as bool? ?? false;

          final result = await fallbackParser.parseTransaction(text);
          
          if (shouldFail) {
            expect(result.hasError, isTrue, reason: 'Should fail for: $text');
          } else {
            // If it shouldn't fail, check that we get some reasonable result
            expect(result.transaction.amount, greaterThanOrEqualTo(0.0));
          }
        }
      });

      test('should handle empty text', () async {
        final result = await fallbackParser.parseTransaction('');
        expect(result.hasError, isTrue);
        expect(result.error, contains('Could not determine transaction type'));
      });

      test('should handle whitespace-only text', () async {
        final result = await fallbackParser.parseTransaction('   \n\t  ');
        expect(result.hasError, isTrue);
      });

      test('should handle text without amounts', () async {
        final result = await fallbackParser.parseTransaction('Went to the store');
        expect(result.hasError, isTrue);
        expect(result.error, contains('Could not extract amount'));
      });

      test('should handle text without transaction type indicators', () async {
        final result = await fallbackParser.parseTransaction('\$25.50 something');
        expect(result.hasError, isTrue);
        expect(result.error, contains('Could not determine transaction type'));
      });
    });

    group('Edge Cases', () {
      test('should handle very small amounts', () async {
        const text = 'Spent \$0.01 on something';
        final result = await fallbackParser.parseTransaction(text);
        
        expect(result.transaction.amount, equals(0.01));
        expect(result.transaction.type, equals(TransactionType.expense));
      });

      test('should handle very large amounts', () async {
        const text = 'Spent \$99999.99 on car';
        final result = await fallbackParser.parseTransaction(text);
        
        expect(result.transaction.amount, equals(99999.99));
        expect(result.transaction.type, equals(TransactionType.expense));
      });

      test('should handle zero amounts', () async {
        const text = 'Free coffee \$0.00';
        final result = await fallbackParser.parseTransaction(text);
        
        expect(result.transaction.amount, equals(0.0));
      });

      test('should handle amounts with commas', () async {
        const text = 'Spent \$1,234.56 on laptop';
        final result = await fallbackParser.parseTransaction(text);
        
        expect(result.transaction.amount, equals(1234.56));
      });

      test('should handle special characters in description', () async {
        const text = 'Café & Restaurant! \$25.50 #food';
        final result = await fallbackParser.parseTransaction(text);
        
        expect(result.transaction.amount, equals(25.50));
        expect(result.transaction.description, contains('café'));
        expect(result.transaction.tags, contains('food'));
      });

      test('should handle multiple amounts (should use first)', () async {
        const text = 'Spent \$10 and \$20 on items';
        final result = await fallbackParser.parseTransaction(text);
        
        expect(result.transaction.amount, equals(10.0));
      });
    });

    group('Real-world Examples', () {
      test('should parse real-world transaction examples', () async {
        final realWorldCases = SampleTransactions.realWorldExamples;
        
        for (final entry in realWorldCases.entries) {
          final testData = entry.value;
          final text = testData['text'] as String;
          final expectedAmount = testData['expected_amount'] as double;
          final expectedCurrency = testData['expected_currency'] as String;

          final result = await fallbackParser.parseTransaction(text);
          
          expect(result.transaction.amount, equals(expectedAmount), reason: 'Amount mismatch for: $text');
          expect(result.transaction.currencyCode, equals(expectedCurrency), reason: 'Currency mismatch for: $text');
          expect(result.isSuccess || result.requiresUserInput, isTrue, reason: 'Should parse successfully: $text');
        }
      });
    });

    group('Reported Issues - Amount Parsing Edge Cases', () {
      test('should parse large whole numbers correctly (not truncated)', () async {
        final testCases = {
          'Spent ¥2500 on dinner': 2500.0,
          'Cost ¥3500 for shopping': 3500.0,
          'Paid ¥10000 for rent': 10000.0,
          'Bought ¥5000 groceries': 5000.0,
          'Expense ¥7500 for travel': 7500.0,
        };

        for (final entry in testCases.entries) {
          final result = await fallbackParser.parseTransaction(entry.key);
          expect(result.transaction.amount, equals(entry.value),
                 reason: 'Amount parsing failed for: ${entry.key}');
        }
      });

      test('should handle amounts with commas correctly', () async {
        final testCases = {
          'Spent ¥2,500 on dinner': 2500.0,
          'Cost ¥10,000 for rent': 10000.0,
          'Paid ¥1,234.56 for shopping': 1234.56,
          'Expense ¥25,000.99 for car': 25000.99,
        };

        for (final entry in testCases.entries) {
          final result = await fallbackParser.parseTransaction(entry.key);
          expect(result.transaction.amount, equals(entry.value),
                 reason: 'Comma handling failed for: ${entry.key}');
        }
      });
    });

    group('Reported Issues - Keyword Conflict Resolution', () {
      test('should prefer food category for grocery shopping phrases', () async {
        final testCases = {
          'grocery shopping ¥100': 'food',
          'food shopping ¥50': 'food',
          'supermarket shopping ¥75': 'food',
          'restaurant shopping ¥25': 'food',
        };

        for (final entry in testCases.entries) {
          final result = await fallbackParser.parseTransaction(entry.key);
          expect(result.transaction.categoryId, equals(entry.value),
                 reason: 'Category conflict resolution failed for: ${entry.key}');
        }
      });

      test('should prefer shopping category for non-food shopping phrases', () async {
        final testCases = {
          'clothes shopping ¥100': 'shopping',
          'electronics shopping ¥500': 'shopping',
          'gadget shopping ¥200': 'shopping',
          'fashion shopping ¥150': 'shopping',
        };

        for (final entry in testCases.entries) {
          final result = await fallbackParser.parseTransaction(entry.key);
          expect(result.transaction.categoryId, equals(entry.value),
                 reason: 'Shopping category detection failed for: ${entry.key}');
        }
      });
    });

    group('Reported Issues - Currency Context Detection', () {
      test('should detect CNY for Chinese context', () async {
        final testCases = {
          'Beijing restaurant ¥45.50': 'CNY',
          'Shanghai taxi ¥25': 'CNY',
          'Guangzhou shopping ¥100': 'CNY',
          'China trip ¥500': 'CNY',
          'Chinese restaurant ¥80': 'CNY',
        };

        for (final entry in testCases.entries) {
          final result = await fallbackParser.parseTransaction(entry.key);
          expect(result.transaction.currencyCode, equals(entry.value),
                 reason: 'CNY detection failed for: ${entry.key}');
        }
      });

      test('should detect JPY for Japanese context', () async {
        final testCases = {
          'Tokyo sushi ¥1200': 'JPY',
          'Kyoto temple ¥500': 'JPY',
          'Osaka shopping ¥800': 'JPY',
          'Japan travel ¥2000': 'JPY',
          'Japanese restaurant ¥900': 'JPY',
        };

        for (final entry in testCases.entries) {
          final result = await fallbackParser.parseTransaction(entry.key);
          expect(result.transaction.currencyCode, equals(entry.value),
                 reason: 'JPY detection failed for: ${entry.key}');
        }
      });
    });

    group('Reported Issues - Negative Number Expense Detection', () {
      test('should detect negative amounts as expenses', () async {
        final testCases = {
          '-¥500 for toys': TransactionType.expense,
          '-\$25.50 coffee': TransactionType.expense,
          '-€100 for shopping': TransactionType.expense,
          '-£75.99 for dinner': TransactionType.expense,
        };

        for (final entry in testCases.entries) {
          final result = await fallbackParser.parseTransaction(entry.key);
          expect(result.transaction.type, equals(entry.value),
                 reason: 'Negative amount expense detection failed for: ${entry.key}');
          // Amount should be stored as positive value
          expect(result.transaction.amount, greaterThan(0),
                 reason: 'Amount should be positive for: ${entry.key}');
        }
      });

      test('should handle negative amounts with various formats', () async {
        final testCases = {
          '- ¥500 for toys': TransactionType.expense,
          '-\$25.50 coffee shop': TransactionType.expense,
          '- €100.00 for shopping': TransactionType.expense,
        };

        for (final entry in testCases.entries) {
          final result = await fallbackParser.parseTransaction(entry.key);
          expect(result.transaction.type, equals(entry.value),
                 reason: 'Negative amount with spaces failed for: ${entry.key}');
        }
      });
    });

    group('Performance Tests', () {
      test('should handle multiple parsing requests efficiently', () async {
        final testTexts = [
          'Coffee \$5.50 at Starbucks',
          'Gas \$40.00 at station',
          'Groceries \$85.30 at store',
          'Dinner \$45.99 at restaurant',
          'Movie \$12.50 at theater',
        ];

        final stopwatch = Stopwatch()..start();

        for (int i = 0; i < 100; i++) {
          for (final text in testTexts) {
            await fallbackParser.parseTransaction(text);
          }
        }

        stopwatch.stop();
        expect(stopwatch.elapsedMilliseconds, lessThan(5000)); // Should complete in reasonable time
      });
    });
  });
}
