import 'package:flutter_test/flutter_test.dart';
import '../../lib/models/parse_result.dart';
import '../../lib/models/transaction_model.dart';
import '../helpers/test_helpers.dart';

void main() {
  group('ParseResult Tests', () {
    late Transaction testTransaction;

    setUp(() {
      testTransaction = TestHelpers.createTestTransaction(
        amount: 25.50,
        type: TransactionType.expense,
        description: 'Test transaction',
        currencyCode: 'USD',
      );
    });

    group('Factory Constructors', () {
      test('ParseResult.success should create successful result', () {
        final result = ParseResult.success(testTransaction);
        
        expect(result.transaction, equals(testTransaction));
        expect(result.needsCategorySelection, isFalse);
        expect(result.error, isNull);
        expect(result.isSuccess, isTrue);
        expect(result.requiresUserInput, isFalse);
        expect(result.hasError, isFalse);
      });

      test('ParseResult.needsCategory should create result requiring category', () {
        final result = ParseResult.needsCategory(testTransaction);
        
        expect(result.transaction, equals(testTransaction));
        expect(result.needsCategorySelection, isTrue);
        expect(result.error, isNull);
        expect(result.isSuccess, isTrue);
        expect(result.requiresUserInput, isTrue);
        expect(result.hasError, isFalse);
      });

      test('ParseResult.failed should create failed result', () {
        const errorMessage = 'Test error message';
        final result = ParseResult.failed(testTransaction, errorMessage);
        
        expect(result.transaction, equals(testTransaction));
        expect(result.needsCategorySelection, isTrue);
        expect(result.error, equals(errorMessage));
        expect(result.isSuccess, isFalse);
        expect(result.requiresUserInput, isTrue);
        expect(result.hasError, isTrue);
      });
    });

    group('Helper Methods', () {
      test('isSuccess should return correct values', () {
        // Successful result
        final successResult = ParseResult.success(testTransaction);
        expect(successResult.isSuccess, isTrue);
        
        // Needs category but no error (still success)
        final needsCategoryResult = ParseResult.needsCategory(testTransaction);
        expect(needsCategoryResult.isSuccess, isTrue);
        
        // Failed result
        final failedResult = ParseResult.failed(testTransaction, 'Error');
        expect(failedResult.isSuccess, isFalse);
      });

      test('requiresUserInput should return correct values', () {
        // Complete success - no user input needed
        final successResult = ParseResult.success(testTransaction);
        expect(successResult.requiresUserInput, isFalse);
        
        // Needs category selection
        final needsCategoryResult = ParseResult.needsCategory(testTransaction);
        expect(needsCategoryResult.requiresUserInput, isTrue);
        
        // Failed result also requires user input
        final failedResult = ParseResult.failed(testTransaction, 'Error');
        expect(failedResult.requiresUserInput, isTrue);
      });

      test('hasError should return correct values', () {
        // Successful results
        final successResult = ParseResult.success(testTransaction);
        expect(successResult.hasError, isFalse);
        
        final needsCategoryResult = ParseResult.needsCategory(testTransaction);
        expect(needsCategoryResult.hasError, isFalse);
        
        // Failed result
        final failedResult = ParseResult.failed(testTransaction, 'Error');
        expect(failedResult.hasError, isTrue);
      });
    });

    group('toString Method', () {
      test('should return properly formatted string for success', () {
        final result = ParseResult.success(testTransaction);
        final stringRepresentation = result.toString();
        
        expect(stringRepresentation, contains('ParseResult'));
        expect(stringRepresentation, contains('transaction:'));
        expect(stringRepresentation, contains('needsCategorySelection: false'));
        expect(stringRepresentation, contains('error: null'));
      });

      test('should return properly formatted string for needsCategory', () {
        final result = ParseResult.needsCategory(testTransaction);
        final stringRepresentation = result.toString();
        
        expect(stringRepresentation, contains('ParseResult'));
        expect(stringRepresentation, contains('needsCategorySelection: true'));
        expect(stringRepresentation, contains('error: null'));
      });

      test('should return properly formatted string for failed', () {
        const errorMessage = 'Test error';
        final result = ParseResult.failed(testTransaction, errorMessage);
        final stringRepresentation = result.toString();
        
        expect(stringRepresentation, contains('ParseResult'));
        expect(stringRepresentation, contains('needsCategorySelection: true'));
        expect(stringRepresentation, contains('error: $errorMessage'));
      });
    });

    group('State Validation', () {
      test('successful result should have consistent state', () {
        final result = ParseResult.success(testTransaction);
        
        expect(result.isSuccess, isTrue);
        expect(result.hasError, isFalse);
        expect(result.error, isNull);
        expect(result.needsCategorySelection, isFalse);
        expect(result.requiresUserInput, isFalse);
      });

      test('needsCategory result should have consistent state', () {
        final result = ParseResult.needsCategory(testTransaction);
        
        expect(result.isSuccess, isTrue);
        expect(result.hasError, isFalse);
        expect(result.error, isNull);
        expect(result.needsCategorySelection, isTrue);
        expect(result.requiresUserInput, isTrue);
      });

      test('failed result should have consistent state', () {
        const errorMessage = 'Test error';
        final result = ParseResult.failed(testTransaction, errorMessage);
        
        expect(result.isSuccess, isFalse);
        expect(result.hasError, isTrue);
        expect(result.error, equals(errorMessage));
        expect(result.needsCategorySelection, isTrue);
        expect(result.requiresUserInput, isTrue);
      });
    });

    group('Edge Cases', () {
      test('should handle transaction with minimal data', () {
        final minimalTransaction = Transaction(
          id: 'test-id',
          amount: 0.0,
          type: TransactionType.expense,
          categoryId: '',
          date: DateTime.now(),
          description: '',
        );
        
        final result = ParseResult.success(minimalTransaction);
        expect(result.transaction, equals(minimalTransaction));
        expect(result.isSuccess, isTrue);
      });

      test('should handle empty error message', () {
        final result = ParseResult.failed(testTransaction, '');
        
        expect(result.error, equals(''));
        expect(result.hasError, isTrue);
        expect(result.isSuccess, isFalse);
      });

      test('should handle very long error message', () {
        final longError = 'A' * 1000; // Very long error message
        final result = ParseResult.failed(testTransaction, longError);
        
        expect(result.error, equals(longError));
        expect(result.hasError, isTrue);
        expect(result.isSuccess, isFalse);
      });

      test('should handle transaction with special characters in description', () {
        final specialTransaction = TestHelpers.createTestTransaction(
          description: 'Test with émojis 🍕💰 and special chars !@#\$%^&*()',
        );
        
        final result = ParseResult.success(specialTransaction);
        expect(result.transaction.description, contains('émojis'));
        expect(result.transaction.description, contains('🍕💰'));
        expect(result.isSuccess, isTrue);
      });
    });

    group('Different Transaction Types', () {
      test('should work with expense transactions', () {
        final expenseTransaction = TestHelpers.createTestTransaction(
          type: TransactionType.expense,
          amount: 50.0,
        );
        
        final result = ParseResult.success(expenseTransaction);
        expect(result.transaction.type, equals(TransactionType.expense));
        expect(result.isSuccess, isTrue);
      });

      test('should work with income transactions', () {
        final incomeTransaction = TestHelpers.createTestTransaction(
          type: TransactionType.income,
          amount: 1000.0,
        );
        
        final result = ParseResult.needsCategory(incomeTransaction);
        expect(result.transaction.type, equals(TransactionType.income));
        expect(result.requiresUserInput, isTrue);
      });

      test('should work with loan transactions', () {
        final loanTransaction = TestHelpers.createTestTransaction(
          type: TransactionType.loan,
          amount: 200.0,
        );
        
        final result = ParseResult.failed(loanTransaction, 'Could not find category');
        expect(result.transaction.type, equals(TransactionType.loan));
        expect(result.hasError, isTrue);
      });
    });

    group('Different Currencies', () {
      test('should work with various currencies', () {
        final currencies = ['USD', 'EUR', 'GBP', 'JPY', 'CNY', 'INR'];
        
        for (final currency in currencies) {
          final transaction = TestHelpers.createTestTransaction(
            currencyCode: currency,
            amount: 100.0,
          );
          
          final result = ParseResult.success(transaction);
          expect(result.transaction.currencyCode, equals(currency));
          expect(result.isSuccess, isTrue);
        }
      });
    });

    group('Using Test Helpers', () {
      test('should work with helper methods', () {
        final successResult = TestHelpers.createSuccessParseResult();
        expect(successResult.isSuccess, isTrue);
        expect(successResult.requiresUserInput, isFalse);
        
        final needsCategoryResult = TestHelpers.createNeedsCategoryParseResult();
        expect(needsCategoryResult.isSuccess, isTrue);
        expect(needsCategoryResult.requiresUserInput, isTrue);
        
        final failedResult = TestHelpers.createFailedParseResult();
        expect(failedResult.isSuccess, isFalse);
        expect(failedResult.hasError, isTrue);
      });

      test('should validate results using helper matchers', () {
        final successResult = TestHelpers.createSuccessParseResult();
        expect(ParseResultMatchers.isSuccess(successResult), isTrue);
        expect(ParseResultMatchers.needsCategory(successResult), isFalse);
        expect(ParseResultMatchers.isFailed(successResult), isFalse);
        
        final needsCategoryResult = TestHelpers.createNeedsCategoryParseResult();
        expect(ParseResultMatchers.isSuccess(needsCategoryResult), isFalse);
        expect(ParseResultMatchers.needsCategory(needsCategoryResult), isTrue);
        expect(ParseResultMatchers.isFailed(needsCategoryResult), isFalse);
        
        final failedResult = TestHelpers.createFailedParseResult();
        expect(ParseResultMatchers.isSuccess(failedResult), isFalse);
        expect(ParseResultMatchers.needsCategory(failedResult), isFalse);
        expect(ParseResultMatchers.isFailed(failedResult), isTrue);
      });
    });
  });
}
