import 'package:flutter_test/flutter_test.dart';
import '../../lib/models/transaction_model.dart';
import '../helpers/test_helpers.dart';

void main() {
  group('Transaction Model Tests', () {
    
    group('Transaction Creation', () {
      test('should create transaction with all required fields', () {
        final date = DateTime.now();
        final transaction = Transaction(
          id: 'test-id',
          amount: 25.50,
          type: TransactionType.expense,
          categoryId: 'food',
          date: date,
          description: 'Coffee at Starbucks',
          tags: ['coffee', 'morning'],
          currencyCode: 'USD',
        );

        expect(transaction.id, equals('test-id'));
        expect(transaction.amount, equals(25.50));
        expect(transaction.type, equals(TransactionType.expense));
        expect(transaction.categoryId, equals('food'));
        expect(transaction.date, equals(date));
        expect(transaction.description, equals('Coffee at Starbucks'));
        expect(transaction.tags, equals(['coffee', 'morning']));
        expect(transaction.currencyCode, equals('USD'));
      });

      test('should use default values for optional fields', () {
        final transaction = Transaction(
          id: 'test-id',
          amount: 100.0,
          type: TransactionType.expense,
          categoryId: 'test-category',
          date: DateTime.now(),
          description: 'Test transaction',
        );

        expect(transaction.tags, equals([]));
        expect(transaction.currencyCode, equals('USD'));
      });

      test('should create transaction with test helper', () {
        final transaction = TestHelpers.createTestTransaction(
          amount: 50.0,
          type: TransactionType.income,
          description: 'Test income',
        );

        expect(transaction.amount, equals(50.0));
        expect(transaction.type, equals(TransactionType.income));
        expect(transaction.description, equals('Test income'));
        expect(transaction.currencyCode, equals('USD'));
      });
    });

    group('Transaction JSON Serialization', () {
      test('should serialize to JSON correctly', () {
        final date = DateTime(2023, 12, 25, 14, 30, 0);
        final transaction = Transaction(
          id: 'test-id',
          amount: 25.50,
          type: TransactionType.expense,
          categoryId: 'food',
          date: date,
          description: 'Coffee',
          tags: ['coffee'],
          currencyCode: 'EUR',
        );

        final json = transaction.toJson();

        expect(json['id'], equals('test-id'));
        expect(json['amount'], equals(25.50));
        expect(json['type'], equals('expense'));
        expect(json['categoryId'], equals('food'));
        expect(json['date'], equals(date.toIso8601String()));
        expect(json['description'], equals('Coffee'));
        expect(json['tags'], equals(['coffee']));
        expect(json['currencyCode'], equals('EUR'));
      });

      test('should deserialize from JSON correctly', () {
        final json = {
          'id': 'test-id',
          'amount': 25.50,
          'type': 'expense',
          'categoryId': 'food',
          'date': '2023-12-25T14:30:00.000Z',
          'description': 'Coffee',
          'tags': ['coffee'],
          'currencyCode': 'EUR',
        };

        final transaction = Transaction.fromJson(json);

        expect(transaction.id, equals('test-id'));
        expect(transaction.amount, equals(25.50));
        expect(transaction.type, equals(TransactionType.expense));
        expect(transaction.categoryId, equals('food'));
        expect(transaction.date, equals(DateTime.parse('2023-12-25T14:30:00.000Z')));
        expect(transaction.description, equals('Coffee'));
        expect(transaction.tags, equals(['coffee']));
        expect(transaction.currencyCode, equals('EUR'));
      });

      test('should handle missing optional fields in JSON', () {
        final json = {
          'id': 'test-id',
          'amount': 100.0,
          'type': 'income',
          'categoryId': 'salary',
          'date': '2023-12-25T14:30:00.000Z',
          'description': 'Monthly salary',
        };

        final transaction = Transaction.fromJson(json);

        expect(transaction.tags, equals([]));
        expect(transaction.currencyCode, equals('USD'));
      });

      test('should handle null tags in JSON', () {
        final json = {
          'id': 'test-id',
          'amount': 100.0,
          'type': 'expense',
          'categoryId': 'food',
          'date': '2023-12-25T14:30:00.000Z',
          'description': 'Lunch',
          'tags': null,
        };

        final transaction = Transaction.fromJson(json);
        expect(transaction.tags, equals([]));
      });

      test('should round-trip correctly through JSON', () {
        final original = TestHelpers.createTestTransaction(
          amount: 123.45,
          type: TransactionType.loan,
          tags: ['personal', 'friend'],
          currencyCode: 'GBP',
        );

        final json = original.toJson();
        final restored = Transaction.fromJson(json);

        expect(TestAssertions.transactionsEqual(original, restored), isTrue);
      });
    });

    group('Transaction copyWith', () {
      late Transaction originalTransaction;

      setUp(() {
        originalTransaction = TestHelpers.createTestTransaction(
          amount: 100.0,
          type: TransactionType.expense,
          description: 'Original description',
          tags: ['tag1', 'tag2'],
          currencyCode: 'USD',
        );
      });

      test('should create copy with updated amount', () {
        final updated = originalTransaction.copyWith(amount: 200.0);

        expect(updated.amount, equals(200.0));
        expect(updated.id, equals(originalTransaction.id));
        expect(updated.type, equals(originalTransaction.type));
        expect(updated.description, equals(originalTransaction.description));
      });

      test('should create copy with updated type', () {
        final updated = originalTransaction.copyWith(type: TransactionType.income);

        expect(updated.type, equals(TransactionType.income));
        expect(updated.amount, equals(originalTransaction.amount));
        expect(updated.description, equals(originalTransaction.description));
      });

      test('should create copy with updated description', () {
        final updated = originalTransaction.copyWith(description: 'New description');

        expect(updated.description, equals('New description'));
        expect(updated.amount, equals(originalTransaction.amount));
        expect(updated.type, equals(originalTransaction.type));
      });

      test('should create copy with updated tags', () {
        final newTags = ['new1', 'new2', 'new3'];
        final updated = originalTransaction.copyWith(tags: newTags);

        expect(updated.tags, equals(newTags));
        expect(updated.amount, equals(originalTransaction.amount));
        expect(updated.type, equals(originalTransaction.type));
      });

      test('should create copy with updated currency', () {
        final updated = originalTransaction.copyWith(currencyCode: 'EUR');

        expect(updated.currencyCode, equals('EUR'));
        expect(updated.amount, equals(originalTransaction.amount));
        expect(updated.type, equals(originalTransaction.type));
      });

      test('should create copy with multiple updated fields', () {
        final newDate = DateTime.now().add(const Duration(days: 1));
        final updated = originalTransaction.copyWith(
          amount: 250.0,
          type: TransactionType.income,
          description: 'Updated description',
          date: newDate,
          currencyCode: 'EUR',
        );

        expect(updated.amount, equals(250.0));
        expect(updated.type, equals(TransactionType.income));
        expect(updated.description, equals('Updated description'));
        expect(updated.date, equals(newDate));
        expect(updated.currencyCode, equals('EUR'));
        expect(updated.id, equals(originalTransaction.id));
        expect(updated.categoryId, equals(originalTransaction.categoryId));
      });

      test('should create identical copy when no fields updated', () {
        final copy = originalTransaction.copyWith();

        expect(TestAssertions.transactionsEqual(originalTransaction, copy), isTrue);
      });
    });

    group('TransactionType Enum', () {
      test('should have correct enum values', () {
        expect(TransactionType.values, contains(TransactionType.expense));
        expect(TransactionType.values, contains(TransactionType.income));
        expect(TransactionType.values, contains(TransactionType.loan));
        expect(TransactionType.values.length, equals(3));
      });

      test('should serialize enum to string correctly', () {
        expect(TransactionType.expense.name, equals('expense'));
        expect(TransactionType.income.name, equals('income'));
        expect(TransactionType.loan.name, equals('loan'));
      });

      test('should deserialize enum from string correctly', () {
        expect(TransactionType.values.byName('expense'), equals(TransactionType.expense));
        expect(TransactionType.values.byName('income'), equals(TransactionType.income));
        expect(TransactionType.values.byName('loan'), equals(TransactionType.loan));
      });
    });

    group('Edge Cases and Validation', () {
      test('should handle zero amount', () {
        final transaction = TestHelpers.createTestTransaction(amount: 0.0);
        expect(transaction.amount, equals(0.0));
      });

      test('should handle very large amounts', () {
        final transaction = TestHelpers.createTestTransaction(amount: 999999.99);
        expect(transaction.amount, equals(999999.99));
      });

      test('should handle very small amounts', () {
        final transaction = TestHelpers.createTestTransaction(amount: 0.01);
        expect(transaction.amount, equals(0.01));
      });

      test('should handle empty description', () {
        final transaction = TestHelpers.createTestTransaction(description: '');
        expect(transaction.description, equals(''));
      });

      test('should handle very long description', () {
        final longDescription = 'A' * 1000;
        final transaction = TestHelpers.createTestTransaction(description: longDescription);
        expect(transaction.description, equals(longDescription));
      });

      test('should handle special characters in description', () {
        const specialDescription = 'Café lunch 🍕💰 !@#\$%^&*()';
        final transaction = TestHelpers.createTestTransaction(description: specialDescription);
        expect(transaction.description, equals(specialDescription));
      });

      test('should handle empty tags list', () {
        final transaction = TestHelpers.createTestTransaction(tags: []);
        expect(transaction.tags, isEmpty);
      });

      test('should handle many tags', () {
        final manyTags = List.generate(50, (index) => 'tag$index');
        final transaction = TestHelpers.createTestTransaction(tags: manyTags);
        expect(transaction.tags, equals(manyTags));
      });

      test('should handle special characters in tags', () {
        final specialTags = ['café', '🍕', 'special-tag', 'tag_with_underscore'];
        final transaction = TestHelpers.createTestTransaction(tags: specialTags);
        expect(transaction.tags, equals(specialTags));
      });
    });

    group('Different Currency Codes', () {
      test('should handle various currency codes', () {
        final currencies = ['USD', 'EUR', 'GBP', 'JPY', 'CNY', 'INR', 'KRW'];
        
        for (final currency in currencies) {
          final transaction = TestHelpers.createTestTransaction(currencyCode: currency);
          expect(transaction.currencyCode, equals(currency));
        }
      });

      test('should handle case sensitivity in currency codes', () {
        final transaction = TestHelpers.createTestTransaction(currencyCode: 'usd');
        expect(transaction.currencyCode, equals('usd'));
      });
    });
  });

  group('Category Model Tests', () {
    
    group('Category Creation', () {
      test('should create category with all fields', () {
        final category = Category(
          id: 'food',
          name: 'Food & Dining',
          icon: '🍽️',
          colorValue: 0xFF2196F3,
          type: TransactionType.expense,
        );

        expect(category.id, equals('food'));
        expect(category.name, equals('Food & Dining'));
        expect(category.icon, equals('🍽️'));
        expect(category.colorValue, equals(0xFF2196F3));
        expect(category.type, equals(TransactionType.expense));
      });

      test('should create category with test helper', () {
        final category = TestHelpers.createTestCategory(
          name: 'Transportation',
          icon: '🚗',
          type: TransactionType.expense,
        );

        expect(category.name, equals('Transportation'));
        expect(category.icon, equals('🚗'));
        expect(category.type, equals(TransactionType.expense));
      });
    });

    group('Category JSON Serialization', () {
      test('should serialize to JSON correctly', () {
        final category = Category(
          id: 'food',
          name: 'Food & Dining',
          icon: '🍽️',
          colorValue: 0xFF2196F3,
          type: TransactionType.expense,
        );

        final json = category.toJson();

        expect(json['id'], equals('food'));
        expect(json['name'], equals('Food & Dining'));
        expect(json['icon'], equals('🍽️'));
        expect(json['colorValue'], equals(0xFF2196F3));
        expect(json['type'], equals('expense'));
      });

      test('should deserialize from JSON correctly', () {
        final json = {
          'id': 'food',
          'name': 'Food & Dining',
          'icon': '🍽️',
          'colorValue': 0xFF2196F3,
          'type': 'expense',
        };

        final category = Category.fromJson(json);

        expect(category.id, equals('food'));
        expect(category.name, equals('Food & Dining'));
        expect(category.icon, equals('🍽️'));
        expect(category.colorValue, equals(0xFF2196F3));
        expect(category.type, equals(TransactionType.expense));
      });

      test('should round-trip correctly through JSON', () {
        final original = TestHelpers.createTestCategory(
          name: 'Entertainment',
          icon: '🎬',
          type: TransactionType.expense,
        );

        final json = original.toJson();
        final restored = Category.fromJson(json);

        expect(TestAssertions.categoriesEqual(original, restored), isTrue);
      });
    });

    group('Category Types', () {
      test('should work with expense categories', () {
        final category = TestHelpers.createTestCategory(type: TransactionType.expense);
        expect(category.type, equals(TransactionType.expense));
      });

      test('should work with income categories', () {
        final category = TestHelpers.createTestCategory(type: TransactionType.income);
        expect(category.type, equals(TransactionType.income));
      });

      test('should work with loan categories', () {
        final category = TestHelpers.createTestCategory(type: TransactionType.loan);
        expect(category.type, equals(TransactionType.loan));
      });
    });

    group('Category Edge Cases', () {
      test('should handle empty name', () {
        final category = TestHelpers.createTestCategory(name: '');
        expect(category.name, equals(''));
      });

      test('should handle very long name', () {
        final longName = 'A' * 100;
        final category = TestHelpers.createTestCategory(name: longName);
        expect(category.name, equals(longName));
      });

      test('should handle special characters in name', () {
        const specialName = 'Café & Restaurant 🍽️';
        final category = TestHelpers.createTestCategory(name: specialName);
        expect(category.name, equals(specialName));
      });

      test('should handle empty icon', () {
        final category = TestHelpers.createTestCategory(icon: '');
        expect(category.icon, equals(''));
      });

      test('should handle various color values', () {
        final colors = [0x00000000, 0xFFFFFFFF, 0xFF2196F3, 0x80FF0000];
        
        for (final color in colors) {
          final category = TestHelpers.createTestCategory(colorValue: color);
          expect(category.colorValue, equals(color));
        }
      });
    });
  });

  group('Test Helper Validation', () {
    test('should create valid test categories', () {
      final categories = TestHelpers.getTestCategories();
      
      expect(categories, isNotEmpty);
      for (final category in categories) {
        expect(category.id, isNotEmpty);
        expect(category.name, isNotEmpty);
        expect(category.icon, isNotEmpty);
        expect(TransactionType.values, contains(category.type));
      }
    });

    test('should create valid test transactions', () {
      final transactions = TestHelpers.getTestTransactionScenarios();
      
      expect(transactions, isNotEmpty);
      for (final transaction in transactions) {
        expect(transaction.id, isNotEmpty);
        expect(transaction.amount, greaterThanOrEqualTo(0.0));
        expect(TransactionType.values, contains(transaction.type));
        expect(transaction.description, isNotEmpty);
        expect(transaction.currencyCode, isNotEmpty);
      }
    });
  });
}
