import 'package:flutter_test/flutter_test.dart';
import '../../lib/utils/currency_utils.dart';

void main() {
  group('CurrencyUtils Tests', () {
    
    group('getCurrencySymbol', () {
      test('should return correct symbol for valid currency codes', () {
        expect(CurrencyUtils.getCurrencySymbol('USD'), equals('\$'));
        expect(CurrencyUtils.getCurrencySymbol('EUR'), equals('€'));
        expect(CurrencyUtils.getCurrencySymbol('GBP'), equals('£'));
        expect(CurrencyUtils.getCurrencySymbol('JPY'), equals('¥'));
        expect(CurrencyUtils.getCurrencySymbol('CNY'), equals('¥'));
        expect(CurrencyUtils.getCurrencySymbol('INR'), equals('₹'));
        expect(CurrencyUtils.getCurrencySymbol('KRW'), equals('₩'));
      });

      test('should handle case insensitive input', () {
        expect(CurrencyUtils.getCurrencySymbol('usd'), equals('\$'));
        expect(CurrencyUtils.getCurrencySymbol('eur'), equals('€'));
        expect(CurrencyUtils.getCurrencySymbol('Gbp'), equals('£'));
        expect(CurrencyUtils.getCurrencySymbol('jPy'), equals('¥'));
      });

      test('should return currency code for unsupported currencies', () {
        expect(CurrencyUtils.getCurrencySymbol('XYZ'), equals('XYZ'));
        expect(CurrencyUtils.getCurrencySymbol('INVALID'), equals('INVALID'));
        expect(CurrencyUtils.getCurrencySymbol(''), equals(''));
      });
    });

    group('formatCurrencyAmount', () {
      test('should format currency with correct symbols', () {
        expect(CurrencyUtils.formatCurrencyAmount(100.0, 'USD'), contains('\$'));
        expect(CurrencyUtils.formatCurrencyAmount(100.0, 'EUR'), contains('€'));
        expect(CurrencyUtils.formatCurrencyAmount(100.0, 'GBP'), contains('£'));
      });

      test('should format amounts with correct decimal places', () {
        // Most currencies use 2 decimal places
        final usdFormatted = CurrencyUtils.formatCurrencyAmount(100.50, 'USD');
        expect(usdFormatted, contains('100.50'));
        
        final eurFormatted = CurrencyUtils.formatCurrencyAmount(250.75, 'EUR');
        expect(eurFormatted, contains('250.75'));
      });

      test('should handle zero decimal currencies correctly', () {
        // JPY, KRW should not have decimal places
        final jpyFormatted = CurrencyUtils.formatCurrencyAmount(1000.0, 'JPY');
        expect(jpyFormatted, contains('1,000'));
        expect(jpyFormatted, isNot(contains('.00')));
        
        final krwFormatted = CurrencyUtils.formatCurrencyAmount(5000.0, 'KRW');
        expect(krwFormatted, contains('5,000'));
        expect(krwFormatted, isNot(contains('.00')));
      });

      test('should handle edge cases', () {
        // Zero amount
        final zeroFormatted = CurrencyUtils.formatCurrencyAmount(0.0, 'USD');
        expect(zeroFormatted, contains('0'));
        
        // Very small amount
        final smallFormatted = CurrencyUtils.formatCurrencyAmount(0.01, 'USD');
        expect(smallFormatted, contains('0.01'));
        
        // Large amount
        final largeFormatted = CurrencyUtils.formatCurrencyAmount(99999.99, 'USD');
        expect(largeFormatted, contains('99,999.99'));
      });

      test('should fallback gracefully for invalid currencies', () {
        final result = CurrencyUtils.formatCurrencyAmount(100.0, 'INVALID');
        expect(result, isNotNull);
        expect(result, contains('100'));
      });
    });

    group('symbolToCurrencyCode', () {
      test('should return correct currency code for symbols', () {
        expect(CurrencyUtils.symbolToCurrencyCode('\$'), equals('USD'));
        expect(CurrencyUtils.symbolToCurrencyCode('€'), equals('EUR'));
        expect(CurrencyUtils.symbolToCurrencyCode('£'), equals('GBP'));
        expect(CurrencyUtils.symbolToCurrencyCode('¥'), anyOf(equals('JPY'), equals('CNY')));
        expect(CurrencyUtils.symbolToCurrencyCode('₹'), equals('INR'));
        expect(CurrencyUtils.symbolToCurrencyCode('₩'), equals('KRW'));
      });

      test('should return USD as fallback for unknown symbols', () {
        expect(CurrencyUtils.symbolToCurrencyCode('X'), equals('USD'));
        expect(CurrencyUtils.symbolToCurrencyCode(''), equals('USD'));
        expect(CurrencyUtils.symbolToCurrencyCode('🔥'), equals('USD'));
      });
    });

    group('isSupportedCurrency', () {
      test('should return true for supported currencies', () {
        expect(CurrencyUtils.isSupportedCurrency('USD'), isTrue);
        expect(CurrencyUtils.isSupportedCurrency('EUR'), isTrue);
        expect(CurrencyUtils.isSupportedCurrency('GBP'), isTrue);
        expect(CurrencyUtils.isSupportedCurrency('JPY'), isTrue);
        expect(CurrencyUtils.isSupportedCurrency('CNY'), isTrue);
        expect(CurrencyUtils.isSupportedCurrency('INR'), isTrue);
      });

      test('should handle case insensitive input', () {
        expect(CurrencyUtils.isSupportedCurrency('usd'), isTrue);
        expect(CurrencyUtils.isSupportedCurrency('eur'), isTrue);
        expect(CurrencyUtils.isSupportedCurrency('Gbp'), isTrue);
      });

      test('should return false for unsupported currencies', () {
        expect(CurrencyUtils.isSupportedCurrency('XYZ'), isFalse);
        expect(CurrencyUtils.isSupportedCurrency('INVALID'), isFalse);
        expect(CurrencyUtils.isSupportedCurrency(''), isFalse);
      });
    });

    group('getSupportedCurrencyCodes', () {
      test('should return list of supported currency codes', () {
        final codes = CurrencyUtils.getSupportedCurrencyCodes();
        expect(codes, isA<List<String>>());
        expect(codes, isNotEmpty);
        expect(codes, contains('USD'));
        expect(codes, contains('EUR'));
        expect(codes, contains('GBP'));
        expect(codes, contains('JPY'));
      });

      test('should return unique currency codes', () {
        final codes = CurrencyUtils.getSupportedCurrencyCodes();
        final uniqueCodes = codes.toSet();
        expect(codes.length, equals(uniqueCodes.length));
      });
    });

    group('getCurrencyName', () {
      test('should return correct currency names', () {
        expect(CurrencyUtils.getCurrencyName('USD'), equals('US Dollar'));
        expect(CurrencyUtils.getCurrencyName('EUR'), equals('Euro'));
        expect(CurrencyUtils.getCurrencyName('GBP'), equals('British Pound'));
        expect(CurrencyUtils.getCurrencyName('JPY'), equals('Japanese Yen'));
        expect(CurrencyUtils.getCurrencyName('CNY'), equals('Chinese Yuan'));
        expect(CurrencyUtils.getCurrencyName('INR'), equals('Indian Rupee'));
      });

      test('should handle case insensitive input', () {
        expect(CurrencyUtils.getCurrencyName('usd'), equals('US Dollar'));
        expect(CurrencyUtils.getCurrencyName('eur'), equals('Euro'));
        expect(CurrencyUtils.getCurrencyName('Gbp'), equals('British Pound'));
      });

      test('should return currency code for unsupported currencies', () {
        expect(CurrencyUtils.getCurrencyName('XYZ'), equals('XYZ'));
        expect(CurrencyUtils.getCurrencyName('INVALID'), equals('INVALID'));
      });
    });

    group('Special Currency Handling', () {
      test('should handle currencies with zero decimal places', () {
        const zeroDecimalCurrencies = ['JPY', 'KRW', 'VND', 'CLP', 'PYG'];
        
        for (final currency in zeroDecimalCurrencies) {
          final formatted = CurrencyUtils.formatCurrencyAmount(1000.0, currency);
          expect(formatted, isNot(contains('.00')));
        }
      });

      test('should handle currencies with same symbols', () {
        // Both JPY and CNY use ¥ symbol
        expect(CurrencyUtils.getCurrencySymbol('JPY'), equals('¥'));
        expect(CurrencyUtils.getCurrencySymbol('CNY'), equals('¥'));
        
        // Symbol to code should return one of them (JPY is first in map)
        expect(CurrencyUtils.symbolToCurrencyCode('¥'), anyOf(equals('JPY'), equals('CNY')));
      });

      test('should handle complex currency symbols', () {
        expect(CurrencyUtils.getCurrencySymbol('AED'), equals('د.إ'));
        expect(CurrencyUtils.getCurrencySymbol('SAR'), equals('﷼'));
        expect(CurrencyUtils.getCurrencySymbol('THB'), equals('฿'));
        expect(CurrencyUtils.getCurrencySymbol('TRY'), equals('₺'));
      });
    });

    group('Error Handling', () {
      test('should handle null-like inputs gracefully', () {
        // Empty strings
        expect(CurrencyUtils.getCurrencySymbol(''), equals(''));
        expect(CurrencyUtils.isSupportedCurrency(''), isFalse);
        expect(CurrencyUtils.getCurrencyName(''), equals(''));
      });

      test('should handle formatting errors gracefully', () {
        // Very large numbers
        final result = CurrencyUtils.formatCurrencyAmount(double.maxFinite, 'USD');
        expect(result, isNotNull);
        expect(result, isA<String>());
      });

      test('should handle special floating point values', () {
        // Infinity should be handled gracefully
        final infinityResult = CurrencyUtils.formatCurrencyAmount(double.infinity, 'USD');
        expect(infinityResult, isNotNull);
        
        // NaN should be handled gracefully
        final nanResult = CurrencyUtils.formatCurrencyAmount(double.nan, 'USD');
        expect(nanResult, isNotNull);
      });
    });

    group('Performance Tests', () {
      test('should handle repeated calls efficiently', () {
        const iterations = 1000;
        final stopwatch = Stopwatch()..start();
        
        for (int i = 0; i < iterations; i++) {
          CurrencyUtils.getCurrencySymbol('USD');
          CurrencyUtils.formatCurrencyAmount(100.0, 'USD');
          CurrencyUtils.isSupportedCurrency('USD');
        }
        
        stopwatch.stop();
        expect(stopwatch.elapsedMilliseconds, lessThan(1000)); // Should complete in under 1 second
      });
    });
  });
}
