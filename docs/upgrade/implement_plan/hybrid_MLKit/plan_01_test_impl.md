import 'package:intl/intl.dart';

class CurrencyUtils {
  // Static map of currency codes to their symbols
  static const Map<String, String> currencySymbols = {
    'USD': '\$',
    'EUR': '€',
    'GBP': '£',
    'JPY': '¥',
    'CNY': '¥',
    'INR': '₹',
    'KRW': '₩',
    'AUD': 'A\$',
    'CAD': 'C\$',
    'CHF': 'CHF',
    'SEK': 'kr',
    'NOK': 'kr',
    'DKK': 'kr',
    'RUB': '₽',
    'BRL': 'R\$',
    'MXN': '\$',
    'ZAR': 'R',
    'SGD': 'S\$',
    'HKD': 'HK\$',
    'NZD': 'NZ\$',
    'THB': '฿',
    'TRY': '₺',
    'PLN': 'zł',
    'CZK': 'Kč',
    'HUF': 'Ft',
    'ILS': '₪',
    'AED': 'د.إ',
    'SAR': '﷼',
    'EGP': '£',
    'QAR': '﷼',
    'KWD': 'د.ك',
    'BHD': '.د.ب',
    'OMR': '﷼',
    'JOD': 'د.ا',
    'LBP': '£',
    'MAD': 'د.م.',
    'TND': 'د.ت',
    'DZD': 'د.ج',
    'LYD': 'ل.د',
    'SDG': 'ج.س.',
    'ETB': 'Br',
    'KES': 'KSh',
    'UGX': 'USh',
    'TZS': 'TSh',
    'GHS': '¢',
    'NGN': '₦',
    'XOF': 'CFA',
    'XAF': 'FCFA',
    'MWK': 'MK',
    'ZMW': 'ZK',
    'BWP': 'P',
    'NAD': '\$',
    'SZL': 'L',
    'LSL': 'L',
    'MZN': 'MT',
    'AOA': 'Kz',
    'CVE': '\$',
    'GMD': 'D',
    'GNF': 'FG',
    'LRD': '\$',
    'SLL': 'Le',
    'STN': 'Db',
    'CDF': 'FC',
    'RWF': 'R₣',
    'BIF': 'FBu',
    'DJF': 'Fdj',
    'ERN': 'Nfk',
    'SOS': 'S',
    'SCR': '₨',
    'MUR': '₨',
    'MGA': 'Ar',
    'KMF': 'CF',
    'MYR': 'RM',
    'IDR': 'Rp',
    'PHP': '₱',
    'VND': '₫',
    'LAK': '₭',
    'KHR': '៛',
    'MMK': 'K',
    'BDT': '৳',
    'LKR': '₨',
    'MVR': '.ރ',
    'NPR': '₨',
    'BTN': 'Nu.',
    'PKR': '₨',
    'AFN': '؋',
    'IRR': '﷼',
    'IQD': 'ع.د',
    'SYP': '£',
    'YER': '﷼',
    'UZS': 'лв',
    'KZT': '₸',
    'KGS': 'лв',
    'TJS': 'SM',
    'TMT': 'T',
    'AZN': '₼',
    'GEL': '₾',
    'AMD': '֏',
    'BGN': 'лв',
    'RON': 'lei',
    'HRK': 'kn',
    'RSD': 'дин.',
    'BAM': 'KM',
    'MKD': 'ден',
    'ALL': 'L',
    'MDL': 'L',
    'UAH': '₴',
    'BYN': 'Br',
    'LTL': 'Lt',
    'LVL': 'Ls',
    'EEK': 'kr',
    'ISK': 'kr',
    'FJD': '\$',
    'PGK': 'K',
    'SBD': '\$',
    'VUV': 'VT',
    'WST': 'T',
    'TOP': 'T\$',
    'TWD': 'NT\$',
    'MNT': '₮',
    'KPW': '₩',
    'CLP': '\$',
    'ARS': '\$',
    'UYU': '\$U',
    'PYG': 'Gs',
    'BOB': 'Bs',
    'PEN': 'S/',
    'COP': '\$',
    'VES': 'Bs',
    'GYD': '\$',
    'SRD': '\$',
    'FKP': '£',
    'SHP': '£',
    'GIP': '£',
    'JEP': '£',
    'GGP': '£',
    'IMP': '£',
    'XCD': '\$',
    'BBD': '\$',
    'BZD': 'BZ\$',
    'BMD': '\$',
    'KYD': '\$',
    'JMD': 'J\$',
    'TTD': 'TT\$',
    'HTG': 'G',
    'DOP': 'RD\$',
    'CUP': '₱',
    'NIO': 'C\$',
    'CRC': '₡',
    'GTQ': 'Q',
    'HNL': 'L',
    'PAB': 'B/.',
    'SVC': '₡',
  };

  // List of supported currencies with their details
  static const List<Map<String, String>> supportedCurrencies = [
    {'code': 'USD', 'symbol': '\$', 'name': 'US Dollar'},
    {'code': 'EUR', 'symbol': '€', 'name': 'Euro'},
    {'code': 'GBP', 'symbol': '£', 'name': 'British Pound'},
    {'code': 'JPY', 'symbol': '¥', 'name': 'Japanese Yen'},
    {'code': 'CNY', 'symbol': '¥', 'name': 'Chinese Yuan'},
    {'code': 'INR', 'symbol': '₹', 'name': 'Indian Rupee'},
    {'code': 'KRW', 'symbol': '₩', 'name': 'South Korean Won'},
    {'code': 'AUD', 'symbol': 'A\$', 'name': 'Australian Dollar'},
    {'code': 'CAD', 'symbol': 'C\$', 'name': 'Canadian Dollar'},
    {'code': 'CHF', 'symbol': 'CHF', 'name': 'Swiss Franc'},
    {'code': 'SEK', 'symbol': 'kr', 'name': 'Swedish Krona'},
    {'code': 'NOK', 'symbol': 'kr', 'name': 'Norwegian Krone'},
    {'code': 'DKK', 'symbol': 'kr', 'name': 'Danish Krone'},
    {'code': 'RUB', 'symbol': '₽', 'name': 'Russian Ruble'},
    {'code': 'BRL', 'symbol': 'R\$', 'name': 'Brazilian Real'},
    {'code': 'MXN', 'symbol': '\$', 'name': 'Mexican Peso'},
    {'code': 'ZAR', 'symbol': 'R', 'name': 'South African Rand'},
    {'code': 'SGD', 'symbol': 'S\$', 'name': 'Singapore Dollar'},
    {'code': 'HKD', 'symbol': 'HK\$', 'name': 'Hong Kong Dollar'},
    {'code': 'NZD', 'symbol': 'NZ\$', 'name': 'New Zealand Dollar'},
    {'code': 'THB', 'symbol': '฿', 'name': 'Thai Baht'},
    {'code': 'TRY', 'symbol': '₺', 'name': 'Turkish Lira'},
    {'code': 'PLN', 'symbol': 'zł', 'name': 'Polish Zloty'},
    {'code': 'CZK', 'symbol': 'Kč', 'name': 'Czech Koruna'},
    {'code': 'HUF', 'symbol': 'Ft', 'name': 'Hungarian Forint'},
    {'code': 'ILS', 'symbol': '₪', 'name': 'Israeli Shekel'},
    {'code': 'AED', 'symbol': 'د.إ', 'name': 'UAE Dirham'},
    {'code': 'SAR', 'symbol': '﷼', 'name': 'Saudi Riyal'},
    {'code': 'MYR', 'symbol': 'RM', 'name': 'Malaysian Ringgit'},
    {'code': 'IDR', 'symbol': 'Rp', 'name': 'Indonesian Rupiah'},
    {'code': 'PHP', 'symbol': '₱', 'name': 'Philippine Peso'},
    {'code': 'VND', 'symbol': '₫', 'name': 'Vietnamese Dong'},
    {'code': 'TWD', 'symbol': 'NT\$', 'name': 'Taiwan Dollar'},
  ];

  /// Get the currency symbol for a given currency code
  /// Returns the symbol if found, otherwise returns the currency code itself
  static String getCurrencySymbol(String currencyCode) {
    return currencySymbols[currencyCode.toUpperCase()] ?? currencyCode;
  }

  /// Format a currency amount with the appropriate symbol and formatting
  /// Uses NumberFormat.currency() for proper locale-specific formatting
  static String formatCurrencyAmount(double amount, String currencyCode) {
    try {
      final symbol = getCurrencySymbol(currencyCode);
      return NumberFormat.currency(
        symbol: symbol,
        decimalDigits: _getDecimalDigits(currencyCode),
      ).format(amount);
    } catch (e) {
      // Fallback to simple formatting if NumberFormat fails
      final symbol = getCurrencySymbol(currencyCode);
      return '$symbol${amount.toStringAsFixed(_getDecimalDigits(currencyCode))}';
    }
  }

  /// Get the number of decimal digits for a currency
  /// Most currencies use 2 decimal places, but some (like JPY, KRW) use 0
  static int _getDecimalDigits(String currencyCode) {
    final noDecimalCurrencies = ['JPY', 'KRW', 'VND', 'CLP', 'PYG', 'RWF', 'UGX', 'KMF', 'GNF', 'MGA', 'XAF', 'XOF'];
    return noDecimalCurrencies.contains(currencyCode.toUpperCase()) ? 0 : 2;
  }

  /// Map currency symbols to currency codes with optional context-aware detection
  /// Used for parsing text that contains currency symbols
  ///
  /// For ambiguous symbols like ¥ (used by both JPY and CNY), the [context] parameter
  /// can be provided to intelligently determine the correct currency based on
  /// contextual clues in the transaction text.
  ///
  /// Example:
  /// ```dart
/// symbolToCurrencyCode('¥', context: 'Beijing restaurant') // Returns 'CNY'
  /// symbolToCurrencyCode('¥', context: 'Tokyo sushi')       // Returns 'JPY'
  /// symbolToCurrencyCode('¥')                               // Returns 'JPY' (default)
  ///
```
  static String symbolToCurrencyCode(String symbol, {String? context}) {
    // Handle ¥ symbol ambiguity with context-aware detection
    if (symbol == '¥' && context != null) {
      final contextLower = context.toLowerCase();

      // Chinese indicators
      const chineseIndicators = [
        'beijing', 'shanghai', 'china', 'chinese', 'rmb', 'yuan', 'cny',
        'guangzhou', 'shenzhen', 'chengdu', 'hangzhou', 'nanjing', 'wuhan',
        'xian', 'tianjin', 'suzhou', 'chongqing', 'shenyang', 'dalian'
      ];

      // Japanese indicators
      const japaneseIndicators = [
        'tokyo', 'japan', 'japanese', 'yen', 'jpy', 'kyoto', 'osaka',
        'yokohama', 'nagoya', 'sapporo', 'kobe', 'fukuoka', 'hiroshima',
        'sendai', 'kitakyushu', 'chiba', 'sakai', 'niigata', 'hamamatsu'
      ];

      // Check for Chinese indicators
      for (final indicator in chineseIndicators) {
        if (contextLower.contains(indicator)) {
          return 'CNY';
        }
      }

      // Check for Japanese indicators
      for (final indicator in japaneseIndicators) {
        if (contextLower.contains(indicator)) {
          return 'JPY';
        }
      }

      // Default to JPY for backward compatibility when no context clues found
      return 'JPY';
    }

    // For non-ambiguous symbols or when no context provided, use original logic
    for (final entry in currencySymbols.entries) {
      if (entry.value == symbol) {
        return entry.key;
      }
    }
    return 'USD'; // Default fallback
  }

  /// Check if a currency code is supported
  static bool isSupportedCurrency(String currencyCode) {
    return currencySymbols.containsKey(currencyCode.toUpperCase());
  }

  /// Get all supported currency codes
  static List<String> getSupportedCurrencyCodes() {
    return supportedCurrencies.map((currency) => currency['code']!).toList();
  }

  /// Get currency name by code
  static String getCurrencyName(String currencyCode) {
    final currency = supportedCurrencies.firstWhere(
      (currency) => currency['code'] == currencyCode.toUpperCase(),
      orElse: () => {'name': currencyCode},
    );
    return currency['name']!;
  }
}

### lib/services/parser/category_keyword_map.dart(MODIFY)

References: 

- lib/services/parser/category_finder_service.dart

Improve the keyword matching algorithm to resolve category conflicts:

1. **Enhance Scoring Algorithm**: Modify the `findCategoryByKeywords` function to better handle keyword conflicts:
   - Add context-aware scoring that considers keyword combinations
   - When multiple categories match, prefer more specific food-related keywords over generic shopping keywords
   - Implement tie-breaking logic: if 'grocery' (food category) and 'shopping' (shopping category) both match, prefer 'food'

2. **Add Compound Keyword Detection**: Implement logic to detect compound phrases:
   - 'grocery shopping' should prioritize 'food' category over 'shopping'
   - 'food shopping' should prioritize 'food' category
   - 'clothes shopping' should prioritize 'shopping' category
   - 'electronics shopping' should prioritize 'shopping' category

3. **Improve Scoring Weights**: Adjust the scoring system:
   - Give higher weight to specific food-related keywords when they appear with generic shopping terms
   - Add bonus points for category-specific compound phrases
   - Implement minimum score difference threshold for confident category selection

4. **Add Category Priority Rules**: Implement category-specific priority rules:
   - Food-related keywords should take precedence over shopping when both are present
   - More specific keywords should outweigh generic ones
   - Brand names should have high confidence scores

These improvements will resolve the 'grocery shopping' category conflict and improve overall keyword matching accuracy.

### test/utils/currency_utils_test.dart(MODIFY)

References: 

- lib/utils/currency_utils.dart(MODIFY)

Add comprehensive tests for the enhanced currency detection functionality:

1. **Context-Aware Currency Detection Tests**: Add tests for the new context parameter:
   - Test `symbolToCurrencyCode('¥', context: 'Beijing restaurant')` returns 'CNY'
   - Test `symbolToCurrencyCode('¥', context: 'Tokyo sushi')` returns 'JPY'
   - Test `symbolToCurrencyCode('¥', context: 'Shanghai taxi')` returns 'CNY'
   - Test `symbolToCurrencyCode('¥', context: 'Kyoto temple')` returns 'JPY'

2. **Backward Compatibility Tests**: Ensure existing functionality still works:
   - Test `symbolToCurrencyCode('¥')` without context returns 'JPY' (default)
   - Test all other currency symbols work unchanged
   - Test edge cases with empty or null context

3. **Context Keyword Tests**: Test various context indicators:
   - Chinese indicators: 'china', 'chinese', 'rmb', 'yuan', 'cny'
   - Japanese indicators: 'japan', 'japanese', 'yen', 'jpy'
   - Case insensitive matching
   - Multiple indicators in same context

4. **Edge Case Tests**: Test boundary conditions:
   - Empty context string
   - Context with no relevant indicators
   - Context with conflicting indicators
   - Very long context strings

These tests will ensure the enhanced currency detection works correctly and maintains backward compatibility.

### test/services/parser/category_finder_service_test.dart(MODIFY)

References: 

- lib/services/parser/category_keyword_map.dart(MODIFY)
- lib/services/parser/category_finder_service.dart

Add comprehensive tests for the improved keyword matching algorithm:

1. **Keyword Conflict Resolution Tests**: Add tests for the specific reported issues:
   - Test `findCategoryByKeywords('grocery shopping')` returns 'food'
   - Test `findCategoryByKeywords('food shopping')` returns 'food'
   - Test `findCategoryByKeywords('clothes shopping')` returns 'shopping'
   - Test `findCategoryByKeywords('electronics shopping')` returns 'shopping'

2. **Compound Phrase Tests**: Test various compound keyword scenarios:
   - Test food-related compounds prioritize food category
   - Test shopping-specific compounds prioritize shopping category
   - Test ambiguous cases are handled appropriately

3. **Scoring Algorithm Tests**: Test the enhanced scoring logic:
   - Test that specific keywords outweigh generic ones
   - Test tie-breaking scenarios
   - Test minimum confidence thresholds
   - Test brand name recognition

4. **Edge Case Tests**: Test boundary conditions:
   - Test single word inputs
   - Test very long descriptions
   - Test descriptions with multiple category indicators
   - Test descriptions with no clear category indicators

5. **Regression Tests**: Ensure existing functionality still works:
   - Test all existing keyword mappings still work correctly
   - Test multilingual keyword support
   - Test brand name recognition

These tests will verify that the improved keyword matching resolves conflicts while maintaining existing functionality.I have created the following plan after thorough exploration and analysis of the codebase. Follow the below plan verbatim. Trust the files and references. Do not re-verify what's written in the plan. Explore only when absolutely necessary. First implement all the proposed file changes and then I'll review all the changes together at the end.

### Observations

Based on my comprehensive analysis of the codebase, I've identified the root causes of the failing tests:

1. **Amount Parsing Regex Issues**: The regex pattern `(\d{1,3}(?:,\d{3})*(?:\.\d{2})?|\d+(?:\.\d{2})?)` in both parsers has flaws that cause "2500" to be parsed as "250" due to greedy matching and improper comma handling.

2. **Keyword Matching Conflicts**: The scoring algorithm in `findCategoryByKeywords` gives "shopping" higher priority than "grocery" when both appear in "grocery shopping", despite "grocery" being more specific to the "food" category.

3. **Currency Symbol Ambiguity**: `CurrencyUtils.symbolToCurrencyCode()` returns the first match for "¥", which is JPY, but should intelligently detect CNY vs JPY based on context.

4. **MLKit Negative Number Handling**: The MLKit parser strips negative signs but doesn't use this information for transaction type detection, missing the opportunity to automatically classify negative amounts as expenses.

5. **Incomplete MLKit Fallback Logic**: The current implementation only falls back to regex parsing on exceptions, but should also fallback when MLKit returns incomplete results (no amount or no entities).

### Approach

The implementation will focus on targeted fixes to the core parsing logic while maintaining backward compatibility. I'll enhance the regex patterns, improve the keyword scoring algorithm, add context-aware currency detection, and strengthen the MLKit-to-fallback cascade. The approach prioritizes fixing the specific failing test cases while adding comprehensive edge case coverage.

### Reasoning

I analyzed the test structure and found comprehensive test suites for MLKit parser, fallback parser, and integration tests. I examined the core implementation files including both parser services, category finder, currency utils, and keyword mapping. I also reviewed the data models and learned category storage to understand the complete data flow. The failing tests clearly indicate specific regex, keyword matching, and currency detection issues that need targeted fixes.

## Mermaid Diagram

sequenceDiagram
    participant User as User Input
    participant MLKit as MLKit Parser
    participant Fallback as Fallback Parser
    participant Regex as Enhanced Regex
    participant Currency as Context-Aware Currency
    participant Keywords as Improved Keywords
    participant Result as Parse Result

    User->>MLKit: "grocery shopping ¥2500"
    
    Note over MLKit: Enhanced MLKit Logic
    MLKit->>MLKit: Check for negative sign
    MLKit->>MLKit: Try MLKit entity extraction
    
    alt MLKit finds entities
        MLKit->>Regex: Extract amount with fixed regex
        Regex-->>MLKit: amount: 2500.0 ✅
        MLKit->>Currency: Resolve ¥ with context
        Currency-->>MLKit: currency: CNY ✅
        MLKit->>Keywords: Find category for "grocery shopping"
        Keywords-->>MLKit: category: "food" ✅
        MLKit->>Result: Success with correct parsing
    else MLKit fails or incomplete
        MLKit->>Fallback: Cascade to fallback parser
        Fallback->>Regex: Extract amount with fixed regex
        Regex-->>Fallback: amount: 2500.0 ✅
        Fallback->>Currency: Resolve ¥ with context
        Currency-->>Fallback: currency: CNY ✅
        Fallback->>Keywords: Find category for "grocery shopping"
        Keywords-->>Fallback: category: "food" ✅
        Fallback->>Result: Success with correct parsing
    end
    
    Result-->>User: Correct transaction: amount=2500, category=food, currency=CNY
    
    Note over User,Result: All reported issues resolved:
    Note over User,Result: ✅ Amount parsing: 2500 not 250
    Note over User,Result: ✅ Category: food not shopping  
    Note over User,Result: ✅ Currency: CNY context-aware
    Note over User,Result: ✅ Negative numbers → expense

## Proposed File Changes

### test/services/parser/fallback_parser_service_test.dart(MODIFY)

References: 

- test/test_data/sample_transactions.dart

Add specific test cases for the reported issues:

1. **Amount Parsing Edge Cases**: Add tests for large whole numbers that are currently failing:
   - `'Spent ¥2500 on dinner'` should parse as 2500.0, not 250
   - `'Cost ¥3500 for shopping'` should parse as 3500.0, not 350
   - `'Paid ¥10000 for rent'` should parse as 10000.0, not 1000

2. **Keyword Conflict Resolution Tests**: Add tests to verify proper category prioritization:
   - `'grocery shopping'` should match 'food' category, not 'shopping'
   - `'food shopping'` should match 'food' category
   - `'clothes shopping'` should match 'shopping' category
   - `'electronics shopping'` should match 'shopping' category

3. **Currency Context Detection Tests**: Add tests for ¥ symbol disambiguation:
   - `'Beijing restaurant ¥45.50'` should detect 'CNY'
   - `'Tokyo sushi ¥1200'` should detect 'JPY'
   - `'Shanghai taxi ¥25'` should detect 'CNY'
   - `'Kyoto temple ¥500'` should detect 'JPY'

4. **Negative Number Expense Detection**: Add tests for negative amounts:
   - `'-¥500 for toys'` should be detected as TransactionType.expense
   - `'-$25.50 coffee'` should be detected as TransactionType.expense

These tests should initially fail, demonstrating the current issues, then pass after implementing the fixes.

### test/services/parser/mlkit_parser_service_test.dart(MODIFY)

References: 

- lib/services/parser/mlkit_parser_service.dart(MODIFY)
- lib/services/parser/fallback_parser_service.dart(MODIFY)

Add corresponding test cases for MLKit parser service to ensure consistency with fallback parser:

1. **MLKit Negative Number Handling**: Add tests to verify that negative numbers are properly detected as expenses in the MLKit parsing path:
   - Test that `-$25.50 coffee` is detected as TransactionType.expense
   - Test that negative amounts in various currencies are handled correctly

2. **MLKit Fallback Integration**: Add tests to verify that MLKit properly falls back to regex parsing when:
   - MLKit returns no entities
   - MLKit returns entities but no money entity
   - MLKit returns money entity but amount extraction fails

3. **Consistency Tests**: Add tests to ensure MLKit and fallback parsers produce consistent results for the same input:
   - Same amount extraction for large numbers
   - Same currency detection logic
   - Same category finding behavior

These tests will verify that the MLKit parser properly handles edge cases and maintains consistency with the fallback parser.

### test/integration/parsing_pipeline_test.dart(MODIFY)

References: 

- lib/services/parser/mlkit_parser_service.dart(MODIFY)
- lib/services/parser/fallback_parser_service.dart(MODIFY)
- lib/services/parser/category_finder_service.dart

Add integration tests for the specific reported issues to ensure end-to-end functionality:

1. **End-to-End Amount Parsing**: Add tests that verify the complete parsing pipeline handles large amounts correctly:
   - Test that `'Dinner ¥2500 #food'` produces a transaction with amount 2500.0
   - Test that the transaction is properly stored and retrieved

2. **End-to-End Category Resolution**: Add tests for keyword conflict scenarios:
   - Test that `'grocery shopping ¥100'` results in 'food' category, not 'shopping'
   - Verify that learned categories override keyword conflicts appropriately

3. **End-to-End Currency Detection**: Add tests for context-aware currency detection:
   - Test transactions with Chinese context (Beijing, Shanghai) resolve ¥ to CNY
   - Test transactions with Japanese context (Tokyo, Kyoto) resolve ¥ to JPY

4. **MLKit-Fallback Pipeline**: Add tests to verify the complete MLKit-to-fallback cascade works correctly:
   - Test scenarios where MLKit fails and fallback succeeds
   - Test scenarios where both parsers handle the same input consistently

These integration tests will ensure that fixes work correctly across the entire parsing pipeline.

### lib/utils/currency_utils.dart(MODIFY)

References: 

- lib/services/parser/mlkit_parser_service.dart(MODIFY)
- lib/services/parser/fallback_parser_service.dart(MODIFY)

Enhance the `symbolToCurrencyCode` method to support context-aware currency detection:

1. **Add Context Parameter**: Modify the method signature to accept an optional context parameter:
   ```dart
   static String symbolToCurrencyCode(String symbol, {String? context})
   ```

2. **Implement Context-Aware Logic**: Add logic to handle the ¥ symbol ambiguity:
   - When symbol is '¥' and context is provided, analyze context for regional indicators
   - Define Chinese indicators: ['beijing', 'shanghai', 'china', 'chinese', 'rmb', 'yuan', 'cny']
   - Define Japanese indicators: ['tokyo', 'japan', 'japanese', 'yen', 'jpy', 'kyoto', 'osaka']
   - Return 'CNY' if Chinese indicators found in context
   - Return 'JPY' if Japanese indicators found in context
   - Default to 'JPY' for backward compatibility when no context clues

3. **Maintain Backward Compatibility**: Ensure the method works without context parameter for existing callers

4. **Add Documentation**: Document the context-aware behavior and provide examples of usage

This enhancement will resolve the ¥ symbol ambiguity issue while maintaining existing functionality.

### lib/services/parser/fallback_parser_service.dart(MODIFY)

References: 

- lib/utils/currency_utils.dart(MODIFY)
- lib/models/transaction_model.dart

Fix the amount extraction regex and improve negative number handling:

1. **Fix Amount Extraction Regex**: Update the regex pattern in `_extractPositiveAmount` method:
   - Change from: `(\d{1,3}(?:,\d{3})*(?:\.\d{2})?|\d+(?:\.\d{2})?)`
   - Change to: `(\d{1,3}(?:,\d{3})*(?:\.\d{1,2})?|\d+(?:\.\d{1,2})?)`
   - Ensure proper comma removal: `amountString.replaceAll(',', '')` before parsing
   - Add validation to ensure the parsed amount matches expected patterns

2. **Enhance Negative Number Detection**: Improve the `_extractAmount` method:
   - Extract the negative sign detection logic into a separate boolean flag
   - Pass this information to `_detectTransactionType` method
   - Ensure negative amounts are properly converted to positive values for storage

3. **Update Currency Detection**: Modify `_extractCurrency` method to use context-aware currency detection:
   - Pass the full transaction text as context to `CurrencyUtils.symbolToCurrencyCode`
   - This will enable proper CNY vs JPY detection based on contextual clues

4. **Improve Transaction Type Detection**: Update `_detectTransactionType` to accept negative number information:
   - If a negative sign was detected, prioritize TransactionType.expense
   - Maintain existing keyword-based detection as secondary logic

These changes will fix the core amount parsing issues and improve transaction type detection accuracy.

### lib/services/parser/mlkit_parser_service.dart(MODIFY)

References: 

- lib/services/parser/fallback_parser_service.dart(MODIFY)
- lib/utils/currency_utils.dart(MODIFY)

Apply consistent fixes to match the fallback parser improvements:

1. **Fix Amount Extraction**: Update the `_extractPositiveAmount` method to use the same improved regex pattern as the fallback parser:
   - Apply the same regex fix for proper handling of large numbers
   - Ensure consistent comma handling and decimal place validation

2. **Enhance Negative Number Handling**: Improve the `_extractAmountWithRegex` method:
   - Extract negative sign detection into a boolean flag
   - Pass this information to the transaction type detection logic
   - Ensure MLKit and fallback parsers handle negative numbers consistently

3. **Improve MLKit Fallback Logic**: Enhance the `_parseWithMLKit` method:
   - Currently only falls back on exceptions; also fallback when MLKit returns incomplete results
   - If `entities.isEmpty`, immediately call fallback parser
   - If no money entity found in entities, call fallback parser
   - If money entity found but amount extraction fails, call fallback parser

4. **Update Currency Detection**: Modify currency extraction methods to use context-aware detection:
   - Pass full transaction text as context to `CurrencyUtils.symbolToCurrencyCode`
   - Ensure consistency with fallback parser currency detection

5. **Enhance Transaction Type Detection**: Update `_detectTransactionType` to handle negative number information:
   - Accept negative number flag as parameter
   - Prioritize expense type when negative sign detected
   - Maintain consistency with fallback parser logic

These changes will ensure MLKit parser has the same improvements as fallback parser and properly cascades to fallback when needed.

### lib/services/parser/category_keyword_map.dart(MODIFY)

References: 

- lib/services/parser/category_finder_service.dart

Improve the keyword matching algorithm to resolve category conflicts:

1. **Enhance Scoring Algorithm**: Modify the `findCategoryByKeywords` function to better handle keyword conflicts:
   - Add context-aware scoring that considers keyword combinations
   - When multiple categories match, prefer more specific food-related keywords over generic shopping keywords
   - Implement tie-breaking logic: if 'grocery' (food category) and 'shopping' (shopping category) both match, prefer 'food'

2. **Add Compound Keyword Detection**: Implement logic to detect compound phrases:
   - 'grocery shopping' should prioritize 'food' category over 'shopping'
   - 'food shopping' should prioritize 'food' category
   - 'clothes shopping' should prioritize 'shopping' category
   - 'electronics shopping' should prioritize 'shopping' category

3. **Improve Scoring Weights**: Adjust the scoring system:
   - Give higher weight to specific food-related keywords when they appear with generic shopping terms
   - Add bonus points for category-specific compound phrases
   - Implement minimum score difference threshold for confident category selection

4. **Add Category Priority Rules**: Implement category-specific priority rules:
   - Food-related keywords should take precedence over shopping when both are present
   - More specific keywords should outweigh generic ones
   - Brand names should have high confidence scores

These improvements will resolve the 'grocery shopping' category conflict and improve overall keyword matching accuracy.

### test/utils/currency_utils_test.dart(MODIFY)

References: 

- lib/utils/currency_utils.dart(MODIFY)

Add comprehensive tests for the enhanced currency detection functionality:

1. **Context-Aware Currency Detection Tests**: Add tests for the new context parameter:
   - Test `symbolToCurrencyCode('¥', context: 'Beijing restaurant')` returns 'CNY'
   - Test `symbolToCurrencyCode('¥', context: 'Tokyo sushi')` returns 'JPY'
   - Test `symbolToCurrencyCode('¥', context: 'Shanghai taxi')` returns 'CNY'
   - Test `symbolToCurrencyCode('¥', context: 'Kyoto temple')` returns 'JPY'

2. **Backward Compatibility Tests**: Ensure existing functionality still works:
   - Test `symbolToCurrencyCode('¥')` without context returns 'JPY' (default)
   - Test all other currency symbols work unchanged
   - Test edge cases with empty or null context

3. **Context Keyword Tests**: Test various context indicators:
   - Chinese indicators: 'china', 'chinese', 'rmb', 'yuan', 'cny'
   - Japanese indicators: 'japan', 'japanese', 'yen', 'jpy'
   - Case insensitive matching
   - Multiple indicators in same context

4. **Edge Case Tests**: Test boundary conditions:
   - Empty context string
   - Context with no relevant indicators
   - Context with conflicting indicators
   - Very long context strings

These tests will ensure the enhanced currency detection works correctly and maintains backward compatibility.

### test/services/parser/category_finder_service_test.dart(MODIFY)

References: 

- lib/services/parser/category_keyword_map.dart(MODIFY)
- lib/services/parser/category_finder_service.dart

Add comprehensive tests for the improved keyword matching algorithm:

1. **Keyword Conflict Resolution Tests**: Add tests for the specific reported issues:
   - Test `findCategoryByKeywords('grocery shopping')` returns 'food'
   - Test `findCategoryByKeywords('food shopping')` returns 'food'
   - Test `findCategoryByKeywords('clothes shopping')` returns 'shopping'
   - Test `findCategoryByKeywords('electronics shopping')` returns 'shopping'

2. **Compound Phrase Tests**: Test various compound keyword scenarios:
   - Test food-related compounds prioritize food category
   - Test shopping-specific compounds prioritize shopping category
   - Test ambiguous cases are handled appropriately

3. **Scoring Algorithm Tests**: Test the enhanced scoring logic:
   - Test that specific keywords outweigh generic ones
   - Test tie-breaking scenarios
   - Test minimum confidence thresholds
   - Test brand name recognition

4. **Edge Case Tests**: Test boundary conditions:
   - Test single word inputs
   - Test very long descriptions
   - Test descriptions with multiple category indicators
   - Test descriptions with no clear category indicators

5. **Regression Tests**: Ensure existing functionality still works:
   - Test all existing keyword mappings still work correctly
   - Test multilingual keyword support
   - Test brand name recognition

These tests will verify that the improved keyword matching resolves conflicts while maintaining existing functionality.